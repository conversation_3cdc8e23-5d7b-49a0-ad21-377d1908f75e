{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/RolesOuter"}, {"$ref": "#/$defs/Uri"}], "properties": {"identity": {"type": "string"}}, "required": ["identity"], "unevaluatedProperties": false, "$anchor": "Job", "$defs": {"Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Name"}, {"$ref": "#/$defs/Description"}], "$anchor": "Documented"}, "RolesOuter": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roles": {"$ref": "#/$defs/RolesInner"}}, "required": ["roles"], "$anchor": "RolesOuter"}, "Uri": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"uri": {"type": "string", "format": "uri"}}, "required": ["uri"], "$anchor": "<PERSON><PERSON>"}, "Name": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Name"}, "Description": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Description"}, "RolesInner": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputMap": {"$ref": "#/$defs/RoleMap"}, "outputMap": {"$ref": "#/$defs/RoleMap"}}, "required": ["inputMap", "outputMap"], "unevaluatedProperties": false, "$anchor": "RolesInner"}, "RoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/RoleLiteral"}, "$anchor": "RoleMap", "propertyNames": {"$ref": "#/$defs/RoleId"}}, "RoleLiteral": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"typeId": {"$ref": "#/$defs/TypeId"}}, "required": ["typeId"]}, {"$ref": "#/$defs/Documented"}], "$anchor": "RoleLiteral"}, "RoleId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "RoleId", "pattern": "^ROLE-.+$"}, "TypeId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "TypeId", "pattern": "^TYPE-.+$"}}}