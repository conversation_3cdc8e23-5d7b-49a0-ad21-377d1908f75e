{"$id": "https://schemas.toolproof.com/v0/Genesis.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "$defs": {"BranchStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/StepKind"}, {"type": "object", "properties": {"cases": {"type": "array", "items": {"$ref": "#/$defs/ConditionalWrapper"}, "minItems": 1, "uniqueItems": true}, "id": {"$ref": "#/$defs/BranchStepId"}, "kind": {"const": "branch"}}, "required": ["id", "kind", "cases"]}], "$anchor": "BranchStep"}, "BranchStepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "BranchStepId", "pattern": "^BRANCHSTEP-.+$"}, "CanonicalRef": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"canonicalRef": {"$comment": "This points to the canonical resource with an identical contentHash as this one. If no such resource exists, this resource will itself be the canonical resource and point to itself (i.e. canonicalRef = id).", "$ref": "#/$defs/ResourceId"}}, "required": ["canonicalRef"], "$anchor": "CanonicalRef"}, "ConditionalWrapper": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"what": {"$ref": "#/$defs/WorkStep"}, "when": {"$ref": "#/$defs/WorkStep"}}, "required": ["when", "what"], "unevaluatedProperties": false, "$anchor": "ConditionalWrapper"}, "CreationContext": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"creationContext": {"$ref": "#/$defs/ResourceSocket"}}, "required": ["creationContext"], "$anchor": "CreationContext"}, "Description": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"description": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "minLength": 1, "semanticValidation": "Ajv custom keyword to verify description."}}, "required": ["description"], "$anchor": "Description"}, "Documented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Name"}, {"$ref": "#/$defs/Description"}], "$anchor": "Documented"}, "Execution": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "properties": {"id": {"$ref": "#/$defs/ExecutionId"}, "jobId": {"$ref": "#/$defs/ResourceId"}}, "required": ["id", "jobId"]}, {"$comment": "This will be overlayed at runtime to specify roleBindings corresponding to the roles of the underlying job.", "$ref": "#/$defs/RoleBindingsOuter"}], "$anchor": "Execution"}, "ExecutionId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ExecutionId", "pattern": "^EXECUTION-.+$"}, "ExtractionSchema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"extractionSchema": {"$ref": "#/$defs/ExtractionSchemaValue"}}, "required": ["extractionSchema"], "$anchor": "ExtractionSchema"}, "ExtractionSchemaValue": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"anyOf": [{"required": ["$id"]}, {"required": ["$anchor"]}, true]}, {"$ref": "#/$defs/IdentityProp"}, {"$ref": "#/$defs/MeritProp"}, {"oneOf": [{"required": ["additionalProperties"]}, {"required": ["unevaluatedProperties"]}]}], "properties": {"$id": {"type": "string", "format": "uri"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "$defs": {"type": "object"}, "type": {"const": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object"}, "additionalProperties": {"const": false}, "unevaluatedProperties": {"const": false}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}}, "required": ["$schema", "type"], "unevaluatedProperties": false, "$anchor": "ExtractionSchemaValue"}, "ForStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/StepKind"}, {"type": "object", "properties": {"case": {"$ref": "#/$defs/ConditionalWrapper"}, "id": {"$ref": "#/$defs/ForStepId"}, "kind": {"const": "for"}}, "required": ["id", "kind", "case"]}], "$anchor": "ForStep"}, "ForStepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ForStepId", "pattern": "^FORSTEP-.+$"}, "FormatBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"properties": {"id": {"$ref": "#/$defs/FormatId"}}, "required": ["id"]}], "$anchor": "FormatBase"}, "FormatData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/FormatBase"}], "unevaluatedProperties": false, "$anchor": "FormatData"}, "FormatId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "FormatId", "pattern": "^FORMAT-.+$"}, "FormatMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/FormatBase"}, {"$ref": "#/$defs/Path"}], "unevaluatedProperties": false, "$anchor": "FormatMeta"}, "Identifiable": {"type": "object", "required": ["id"], "$anchor": "Identifiable"}, "IdentifiableDocumented": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/Documented"}], "$anchor": "IdentifiableDocumented"}, "IdentityProp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"properties": {"type": "object", "properties": {"identity": {"$ref": "#/$defs/IdentityValue"}}, "required": ["identity"]}, "required": {"type": "array", "contains": {"const": "identity"}, "items": {"type": "string"}, "uniqueItems": true}}, "required": ["required", "properties"], "$anchor": "IdentityProp"}, "IdentityValue": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"properties": {"type": {"enum": ["string", "integer", "boolean"]}}, "required": ["type"]}, {"properties": {"enum": {"type": "array", "items": {"type": "string"}, "minItems": 1}}, "required": ["enum"]}], "$anchor": "IdentityValue"}, "Job": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/RolesOuter"}, {"$ref": "#/$defs/Uri"}], "properties": {"identity": {"type": "string"}}, "required": ["identity"], "unevaluatedProperties": false, "$anchor": "Job"}, "JsonValue": {"$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/JsonValue"}}, {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonValue"}}], "$anchor": "JsonValue"}, "MeritProp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"properties": {"type": "object", "properties": {"merit": {"$ref": "#/$defs/MeritValue"}}}}, "$anchor": "MeritProp"}, "MeritValue": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"properties": {"type": {"enum": ["number", "integer"]}}, "required": ["type"]}, {"properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}, "required": ["enum"]}], "$anchor": "MeritValue"}, "Name": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "semanticValidation": "Ajv custom keyword to verify name."}}, "required": ["name"], "$anchor": "Name"}, "Path": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "$anchor": "Path"}, "PendingRef": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"pendingRef": {"$comment": "This points to a resource created at a previous step in the same workflow. This resource is not yet realized at the time of defining the workflow, but will be realized when the step using this resource is executed.The Engine resolves this pointer at runtime.", "$ref": "#/$defs/ResourceSocket"}}, "required": ["pendingRef"], "$anchor": "PendingRef"}, "ResourceBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"properties": {"id": {"$ref": "#/$defs/ResourceId"}}, "required": ["id"]}, {"properties": {"typeId": {"$ref": "#/$defs/TypeId"}}, "required": ["typeId"]}, {"$ref": "#/$defs/CreationContext"}], "$anchor": "ResourceBase"}, "ResourceCanonicalMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "unevaluatedProperties": false, "$anchor": "ResourceCanonicalMeta", "$ref": "#/$defs/ResourceCanonicalMetaBase"}, "ResourceCanonicalMetaBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "realized"}}, "required": ["kind"]}, {"$ref": "#/$defs/Path"}, {"$ref": "#/$defs/Timestamp"}], "$anchor": "ResourceCanonicalMetaBase"}, "ResourceData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceCanonicalMetaBase"}, {"properties": {"extractedData": {"type": "object", "properties": {"identity": {"type": ["string", "integer", "boolean"]}}, "required": ["identity"], "additionalProperties": {"$ref": "#/$defs/JsonValue"}, "$comment": "This will be overlayed at runtime to match the data structure of the underlying type's extractionSchema. At compile time, we guarantee it has an identity property."}}, "required": ["extractedData"]}], "unevaluatedProperties": false, "$anchor": "ResourceData"}, "ResourceId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "ResourceId", "$comment": "", "pattern": "^RESOURCE-.+$"}, "ResourceKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"enum": ["potential-input", "potential-output", "realized"]}}, "required": ["kind"], "$anchor": "ResourceKind"}, "ResourceMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/$defs/ResourcePotentialInput"}, {"$ref": "#/$defs/ResourcePotentialOutput"}, {"$ref": "#/$defs/ResourceData"}]}, "propertyNames": {"$ref": "#/$defs/RoleId"}}, "$anchor": "ResourceMap", "propertyNames": {"$ref": "#/$defs/ExecutionId"}}, "ResourceMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CanonicalRef"}, {"$ref": "#/$defs/Timestamp"}], "unevaluatedProperties": false, "$anchor": "ResourceMeta"}, "ResourcePotentialInput": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "potential-input"}}, "required": ["kind"]}, {"$ref": "#/$defs/PendingRef"}], "unevaluatedProperties": false, "$anchor": "ResourcePotentialInput"}, "ResourcePotentialOutput": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"properties": {"kind": {"const": "potential-output"}}, "required": ["kind"]}], "unevaluatedProperties": false, "$anchor": "ResourcePotentialOutput"}, "ResourceSocket": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"executionId": {"$ref": "#/$defs/ExecutionId"}, "roleId": {"$ref": "#/$defs/RoleId"}}, "required": ["roleId", "executionId"], "$anchor": "ResourceSocket"}, "RoleBindingMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/ResourceId"}, "$anchor": "RoleBindingMap", "propertyNames": {"$ref": "#/$defs/RoleId"}}, "RoleBindingsInner": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}, "outputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}}, "required": ["inputBindingMap", "outputBindingMap"], "unevaluatedProperties": false, "$anchor": "RoleBindingsInner"}, "RoleBindingsOuter": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roleBindings": {"$ref": "#/$defs/RoleBindingsInner"}}, "required": ["roleB<PERSON>ings"], "$anchor": "RoleBindingsOuter"}, "RoleId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "RoleId", "pattern": "^ROLE-.+$"}, "RoleLiteral": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"properties": {"typeId": {"$ref": "#/$defs/TypeId"}}, "required": ["typeId"]}, {"$ref": "#/$defs/Documented"}], "$anchor": "RoleLiteral"}, "RoleMap": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "additionalProperties": {"$ref": "#/$defs/RoleLiteral"}, "$anchor": "RoleMap", "propertyNames": {"$ref": "#/$defs/RoleId"}}, "RolesInner": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"inputMap": {"$ref": "#/$defs/RoleMap"}, "outputMap": {"$ref": "#/$defs/RoleMap"}}, "required": ["inputMap", "outputMap"], "unevaluatedProperties": false, "$anchor": "RolesInner"}, "RolesOuter": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"roles": {"$ref": "#/$defs/RolesInner"}}, "required": ["roles"], "$anchor": "RolesOuter"}, "Step": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false, "$anchor": "Step"}, "StepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStepId"}, {"$ref": "#/$defs/BranchStepId"}, {"$ref": "#/$defs/WhileStepId"}, {"$ref": "#/$defs/ForStepId"}], "$anchor": "StepId"}, "StepKind": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"kind": {"type": "string", "enum": ["work", "branch", "while", "for"]}}, "required": ["kind"], "$anchor": "<PERSON><PERSON><PERSON>"}, "Timestamp": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}}, "required": ["timestamp"], "$anchor": "Timestamp"}, "TypeBase": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"properties": {"formatId": {"$ref": "#/$defs/FormatId"}, "id": {"$ref": "#/$defs/TypeId"}}, "required": ["id", "formatId"]}], "$anchor": "TypeBase"}, "TypeData": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/TypeBase"}, {"$ref": "#/$defs/ExtractionSchema"}], "$anchor": "TypeData"}, "TypeId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "TypeId", "pattern": "^TYPE-.+$"}, "TypeMeta": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/TypeBase"}, {"$ref": "#/$defs/Path"}, {"if": {"properties": {"formatId": {"const": "FORMAT-ApplicationJson"}}, "$comment": "If formatId is FORMAT-ApplicationJson, then uri must not be present, but if formatId is not FORMAT-ApplicationJson, then uri must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor."}, "then": {"not": {"$ref": "#/$defs/Uri"}}, "else": {"$ref": "#/$defs/Uri"}}], "unevaluatedProperties": false, "$anchor": "TypeMeta"}, "Uri": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"uri": {"type": "string", "format": "uri"}}, "required": ["uri"], "$anchor": "<PERSON><PERSON>"}, "WhileStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/StepKind"}, {"type": "object", "properties": {"case": {"$ref": "#/$defs/ConditionalWrapper"}, "id": {"$ref": "#/$defs/WhileStepId"}, "kind": {"const": "while"}}, "required": ["id", "kind", "case"]}], "$anchor": "WhileStep"}, "WhileStepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WhileStepId", "pattern": "^WHILESTEP-.+$"}, "WorkStep": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/StepKind"}, {"type": "object", "properties": {"execution": {"$ref": "#/$defs/Execution"}, "id": {"$ref": "#/$defs/WorkStepId"}, "kind": {"const": "work"}}, "required": ["id", "kind", "execution"]}], "$anchor": "WorkStep"}, "WorkStepId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkStepId", "pattern": "^WORKSTEP-.+$"}, "Workflow": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "properties": {"id": {"$ref": "#/$defs/WorkflowId"}, "steps": {"type": "array", "items": {"$ref": "#/$defs/Step"}, "uniqueItems": true}}, "required": ["id", "steps"]}], "unevaluatedProperties": false, "$anchor": "Workflow"}, "WorkflowId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkflowId", "pattern": "^WORKFLOW-.+$"}, "WorkflowSpec": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"properties": {"id": {"$ref": "#/$defs/WorkflowSpecId"}, "resourceMaps": {"type": "array", "items": {"$ref": "#/$defs/ResourceMap"}, "uniqueItems": true}, "workflow": {"$ref": "#/$defs/Workflow"}}, "required": ["id", "workflow", "resourceMaps"]}], "$anchor": "WorkflowSpec"}, "WorkflowSpecId": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$anchor": "WorkflowSpecId", "pattern": "^WORKFLOWSPEC-.+$"}}, "$comment": "This schema defines all genesis schemas used throughout the Toolproof ecosystem. The genesis schemas themselves are defined in $defs.<ResourceType>.extractionSchema. The build process (via extractSchemas.js) extracts these schemas and writes them to a separate file (src/schemas/Genesis.json). The reason for this indirection is to have all these schema envelopes validate positively against the schema at $defs/TypeData, effectively making them ResourceTypes, which are first-class citizens in the Toolproof ecosystem."}