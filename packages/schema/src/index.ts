// Re-export JSON schemas via .ts shims to avoid .json re-exports in declarations
export { default as GenesisSchema } from './schemas/Genesis.js';
export { default as JobSchema } from './schemas/Job.js';

export type {
  ResourceData_Job as ResourceData_JobJson
} from './_lib/types/ResourceData_Job.js';

export type {
  Identifiable as IdentifiableJson,
  Documented as DocumentedJson,
  IdentifiableDocumented as IdentifiableDocumentedJson,
  FormatId as FormatId<PERSON>son,
  FormatMeta as FormatMetaJson,
  FormatData as FormatDataJson,
  ExtractionSchema as ExtractionSchemaJson,
  ExtractionSchemaValue as ExtractionSchemaValueJson,
  IdentityProp as IdentityPropJson,
  MeritProp as MeritProp<PERSON>son,
  TypeId as TypeId<PERSON>son,
  TypeMeta as TypeMeta<PERSON>son,
  TypeData as TypeData<PERSON>son,
  RoleId as RoleIdJson,
  RoleLiteral as RoleLiteral<PERSON>son,
  ExecutionId as ExecutionId<PERSON>son,
  Execution as Execution<PERSON><PERSON>,
  ConditionalWrapper as Conditional<PERSON><PERSON><PERSON><PERSON><PERSON>,
  RoleMap as <PERSON>Map<PERSON><PERSON>,
  RolesOuter as RolesOuter<PERSON><PERSON>,
  RoleBindingMap as RoleBindingMap<PERSON>son,
  RoleBindingsOuter as RoleBindingsOuterJson,
  ResourceId as ResourceIdJson,
  WorkStepId as WorkStepIdJson,
  BranchStepId as BranchStepIdJson,
  WhileStepId as WhileStepIdJson,
  ForStepId as ForStepIdJson,
  WorkStep as WorkStepJson,
  BranchStep as BranchStepJson,
  WhileStep as WhileStepJson,
  ForStep as ForStepJson,
  StepId as StepIdJson,
  Step as StepJson,
  ResourceSocket as ResourceSocketJson,
  ResourcePotentialInput as ResourcePotentialInputJson,
  ResourcePotentialOutput as ResourcePotentialOutputJson,
  ResourceMeta as ResourceMetaJson,
  ResourceData as ResourceDataJson,
  ResourceMap as ResourceMapJson,
  WorkflowId as WorkflowIdJson,
  Workflow as WorkflowJson,
  WorkflowSpecId as WorkflowSpecIdJson,
  WorkflowSpec as WorkflowSpecJson,
  Job as JobJson,
  JsonValue as JsonValueJson,
} from './_lib/types/types.js';

// Re-export brand factories so consumers can construct branded IDs at runtime.
export {
  unsafeBrand,
  asTypeId,
  asRoleId,
  asExecutionId,
  asResourceId,
  asWorkStepId,
  asBranchStepId,
  asForStepId,
  asFormatId,
  asWhileStepId,
  asWorkflowId,
  asWorkflowSpecId,
  asTypeIds,
  asRoleIds,
  asExecutionIds,
  asResourceIds,
  asWorkStepIds,
  asBranchStepIds,
  asForStepIds,
  asFormatIds,
  asWhileStepIds,
  asWorkflowIds,
  asWorkflowSpecIds,
} from './scripts/brandFactories.js';
