import fs from 'fs';
import path from 'path';

/**
 * Generic extractor: given a subschema name that exists under Genesis.json $defs,
 * produce a standalone JSON Schema file that contains that subschema plus an inner $defs
 * object holding all direct & transitive local $defs dependencies (by #/$defs/... $ref).
 *
 * Genesis.json itself is left unchanged.
 *
 * Usage:
 *   node ./dist/scripts/extractSubschemaWithDefs.js --name <DefName>
 *
 * Writes: src/schemas/<DefName>.json
 */
async function main() {
  const { name } = parseArgs(process.argv.slice(2));
  if (!name) {
    console.error('Missing --name <DefName> argument');
    process.exit(1);
  }

  const projectRoot = process.cwd();
  const schemasDir = path.join(projectRoot, 'src', 'schemas');
  const genesisPath = path.join(schemasDir, 'Genesis.json');
  const outPath = path.join(schemasDir, `${name}.json`);

  if (!fs.existsSync(genesisPath)) {
    console.error(`Genesis.json not found at ${genesisPath}`);
    process.exit(1);
  }

  const raw = fs.readFileSync(genesisPath, 'utf-8');
  const genesis = JSON.parse(raw);
  const rootDefs: Record<string, any> | undefined = genesis.$defs;
  if (!rootDefs || typeof rootDefs !== 'object') {
    console.error('No $defs object found in Genesis.json');
    process.exit(1);
  }

  const target = rootDefs[name];
  if (!target) {
    console.error(`Subschema named '${name}' not found under $defs in Genesis.json`);
    process.exit(1);
  }

  // Collect transitive local $defs names referenced by target
  const needed = collectLocalDefClosure(target, rootDefs);

  // Build output schema: clone target and attach collected subset as $defs
  const targetClone = deepClone(target);
  const defsOut: Record<string, any> = {};
  for (const defName of needed) {
    // Avoid including the target itself inside its own $defs (mirrors previous pattern)
    if (defName === name) continue;
    const defSchema = rootDefs[defName];
    if (defSchema === undefined) {
      console.error(`Warning: referenced def '${defName}' missing in root $defs (skipped).`);
      continue;
    }
    defsOut[defName] = deepClone(defSchema);
  }

  // Merge any pre-existing inner $defs of targetClone (if present) giving precedence to collected ones
  const existingInner = isObject(targetClone.$defs) ? targetClone.$defs : {};
  targetClone.$defs = { ...existingInner, ...defsOut };

  fs.writeFileSync(outPath, JSON.stringify(targetClone, null, 2) + '\n');
  console.log(`Extracted subschema '${name}' with ${needed.size} transitive local defs -> ${outPath}`);
}

// ---- Helpers ----
function parseArgs(args: string[]): { name?: string } {
  let name = "";
  for (let i = 0; i < args.length; i++) {
    const a = args[i] || "";
    if (a === '--name') {
      name = args[i + 1] || "";
      i++;
    } else if (a.startsWith('--name=')) {
      name = a.split('=')[1] || "";
    }
  }
  return { name };
}

function isObject(v: any): v is Record<string, any> {
  return v !== null && typeof v === 'object' && !Array.isArray(v);
}

function deepClone<T>(v: T): T {
  if (Array.isArray(v)) return v.map((x) => deepClone(x)) as any;
  if (isObject(v)) {
    const out: Record<string, any> = {};
    for (const k of Object.keys(v)) out[k] = deepClone((v as any)[k]);
    return out as any;
  }
  return v;
}

function extractLocalDefName(ref: string): string | null {
  // Accept refs like '#/$defs/Name' only (single-level under $defs)
  if (!ref || !ref.startsWith('#/')) return null;
  const parts = ref.slice(2).split('/'); // remove '#/'
  if (parts.length !== 2) return null;
  if (parts[0] !== '$defs') return null;
  // Decode JSON Pointer tokens for the def name
  const name = parts[1]?.replace(/~1/g, '/').replace(/~0/g, '~') || "";
  return name;
}

function collectLocalDefClosure(node: any, rootDefs: Record<string, any>): Set<string> {
  const needed = new Set<string>();
  const queue: string[] = [];

  function visit(n: any) {
    if (Array.isArray(n)) {
      for (const item of n) visit(item);
      return;
    }
    if (!isObject(n)) return;
    if (typeof n.$ref === 'string') {
      const name = extractLocalDefName(n.$ref);
      if (name && !needed.has(name)) {
        needed.add(name);
        queue.push(name);
      }
    }
    for (const val of Object.values(n)) visit(val);
  }

  visit(node);

  while (queue.length > 0) {
    const name = queue.shift()!;
    const def = rootDefs[name];
    if (!def) continue; // Missing def handled earlier
    visit(def);
  }

  return needed;
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
