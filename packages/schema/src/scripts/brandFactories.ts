// Runtime factories for branded ID string types.
// Note: The branded types are declared in src/types/types.d.ts by the generator.
// We import them as type-only so there is no runtime dependency on the .d.ts file.

import type {
  ExecutionId,
  ResourceId,
  WorkStepId,
  BranchStepId,
  ForStepId,
  FormatId,
  TypeId,
  RoleId,
  WhileStepId,
  WorkflowId,
  WorkflowSpecId
} from "../_lib/types/types.js";

// Optional runtime validation toggle. Off by default.
// Enable by setting TP_BRAND_VALIDATE=1 (or NODE_ENV=development) when running.
const shouldValidate: boolean =
  (typeof process !== "undefined" && !!process.env &&
    (process.env.TP_BRAND_VALIDATE === "1" || process.env.NODE_ENV === "development")) ||
  false;

function makeFactory<T extends string>(name: string, pattern?: RegExp) {
  return (s: string): T => {
    if (shouldValidate && pattern && !pattern.test(s)) {
      throw new TypeError(
        `Invalid ${name}: "${s}" does not match ${String(pattern)}`
      );
    }
    return s as T;
  };
}

/** Generic unsafe brand helper when a bespoke factory is not available. */
export function unsafeBrand<T extends string>(s: string): T {
  return s as T;
}

// Known patterns from schemas (when available)
const RE_TYPE_ID = /^TYPE-.+$/;
const RE_SIGNATURE_ID = /^SIGNATURE-.+$/;
const RE_JOB_ID = /^JOB-.+$/;
const RE_ROLE_ID = /^ROLE-.+$/;

// Factories for each Id-like type
export const asTypeId = makeFactory<TypeId>("TypeId", RE_TYPE_ID);
export const asRoleId = makeFactory<RoleId>("RoleId", RE_ROLE_ID);

// IDs without strict regex patterns in the current schemas: keep unchecked casts.
export const asExecutionId = makeFactory<ExecutionId>("ExecutionId");
export const asResourceId = makeFactory<ResourceId>("ResourceId");
export const asWorkStepId = makeFactory<WorkStepId>("WorkStepId");
export const asBranchStepId = makeFactory<BranchStepId>("BranchStepId");
export const asForStepId = makeFactory<ForStepId>("ForStepId");
export const asFormatId = makeFactory<FormatId>("FormatId");
export const asWhileStepId = makeFactory<WhileStepId>("WhileStepId");
export const asWorkflowId = makeFactory<WorkflowId>("WorkflowId");
export const asWorkflowSpecId = makeFactory<WorkflowSpecId>(
  "WorkflowSpecId"
);

// Batch helpers
export const asTypeIds = (xs: string[]): TypeId[] => xs.map(asTypeId);
export const asRoleIds = (xs: string[]): RoleId[] => xs.map(asRoleId);
export const asExecutionIds = (xs: string[]): ExecutionId[] =>
  xs.map(asExecutionId);
export const asResourceIds = (xs: string[]): ResourceId[] =>
  xs.map(asResourceId);
export const asWorkStepIds = (xs: string[]): WorkStepId[] =>
  xs.map(asWorkStepId);
export const asBranchStepIds = (xs: string[]): BranchStepId[] =>
  xs.map(asBranchStepId);
export const asForStepIds = (xs: string[]): ForStepId[] => xs.map(asForStepId);
export const asFormatIds = (xs: string[]): FormatId[] => xs.map(asFormatId);
export const asWhileStepIds = (xs: string[]): WhileStepId[] =>
  xs.map(asWhileStepId);
export const asWorkflowIds = (xs: string[]): WorkflowId[] =>
  xs.map(asWorkflowId);
export const asWorkflowSpecIds = (xs: string[]): WorkflowSpecId[] =>
  xs.map(asWorkflowSpecId);
