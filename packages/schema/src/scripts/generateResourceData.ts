import fs from 'fs';
import path from 'path';
import { compileFromFile } from 'json-schema-to-typescript';

/**
 * Generate a typed ResourceData variant where `extractedData` is typed to a specific schema
 * extracted under `src/schemas/<name>.json`.
 *
 * Usage: node ./dist/scripts/generateResourceData_js --name Job
 */
async function main() {
    const { name } = parseArgs(process.argv.slice(2));
    if (!name) {
        console.error('Missing --name <SchemaBasename> argument');
        process.exit(1);
    }

    const projectRoot = process.cwd();
    const schemasDir = path.join(projectRoot, 'src', 'schemas');
    const inPath = path.join(schemasDir, `${name}.json`);

    if (!fs.existsSync(inPath)) {
        console.error(`Schema file not found: ${inPath}`);
        process.exit(1);
    }

    // Basic validation against the expected shape of ExtractionSchema.
    const raw = fs.readFileSync(inPath, 'utf8');
    let parsed: any = null;
    try {
        parsed = JSON.parse(raw);
    } catch (e) {
        console.error(`Failed to parse JSON schema ${inPath}:`, e);
        process.exit(1);
    }

    // Minimal checks that roughly match the ExtractionSchema constraints used elsewhere.
    if (parsed.$schema && parsed.$schema !== 'https://json-schema.org/draft/2020-12/schema') {
        console.warn(`Warning: schema $schema is '${parsed.$schema}', expected draft 2020-12. Proceeding anyway.`);
    }
    if (parsed.type && parsed.type !== 'object') {
        console.warn(`Warning: ExtractionSchema usually has type: 'object' but this schema has type: '${parsed.type}'. Proceeding.`);
    }

    // Ensure output folder exists
    const outDir = path.join(projectRoot, 'src', '_lib', 'types');
    fs.mkdirSync(outDir, { recursive: true });

    const outName = `ResourceData_${name}.d.ts`;
    const outPath = path.join(outDir, outName);
    const outJsName = `ResourceData_${name}.js`;
    const outJsPath = path.join(outDir, outJsName);

    // Build a composite schema in src/schemas so that local $ref paths are simple
    const schemasOutDir = path.join(projectRoot, 'src', 'schemas');
    const compositePath = path.join(schemasOutDir, `.composite.ResourceData_${name}.json`);

    const compositeSchema = {
        $schema: 'https://json-schema.org/draft/2020-12/schema',
        title: `ResourceData_${name}`,
        type: 'object',
        allOf: [
            { $ref: './Genesis.json#/$defs/ResourceCanonicalMetaBase' },
            {
                type: 'object',
                required: ['extractedData'],
                properties: {
                    extractedData: { $ref: `./${name}.json` }
                },
                unevaluatedProperties: false
            }
        ],
        unevaluatedProperties: false
    } as const;

    // Write composite schema
    fs.writeFileSync(compositePath, JSON.stringify(compositeSchema, null, 2), 'utf8');

    try {
        // Compile to TypeScript declarations
        let ts = await compileFromFile(compositePath, {
            bannerComment: '',
            declareExternallyReferenced: true,
            unreachableDefinitions: true,
            $refOptions: {
                resolve: {
                    file: { order: 1 },
                    http: false,
                    https: false
                }
            } as any
        });

        // Remove noisy index signatures that make types too permissive
        ts = ts.replace(/\n\s*\[k:\s*string\]:\s*unknown;\n/g, '\n');

        // Ensure it is a module
        if (!/\bexport\b/.test(ts)) {
            ts += '\nexport {}\n';
        }

        const header = '// Auto-generated strict composite type. Do not edit.\n';
        fs.writeFileSync(outPath, header + ts, 'utf8');
        console.log(`Wrote ${outPath}`);

        // Ensure a runtime-resolvable JS shim exists alongside the .d.ts for NodeNext resolution
        if (!fs.existsSync(outJsPath)) {
            fs.writeFileSync(outJsPath, 'export {}\n', 'utf8');
            console.log(`Wrote ${outJsPath}`);
        }

        // Also copy both files into dist so consumers can resolve the module and its types
        const distLibDir = path.join(projectRoot, 'dist', '_lib', 'types');
        fs.mkdirSync(distLibDir, { recursive: true });
        const distDtsPath = path.join(distLibDir, outName);
        const distJsPath = path.join(distLibDir, outJsName);
        fs.writeFileSync(distDtsPath, header + ts, 'utf8');
        fs.writeFileSync(distJsPath, 'export {}\n', 'utf8');
        console.log(`Wrote ${distDtsPath}`);
        console.log(`Wrote ${distJsPath}`);
    } finally {
        // Cleanup composite schema file
        try { fs.unlinkSync(compositePath); } catch {}
    }
}

function parseArgs(args: string[]): { name?: string } {
    let name = "";
    for (let i = 0; i < args.length; i++) {
        const a = args[i] || "";
        if (a === '--name') {
            name = args[i + 1] || "";
            i++;
        } else if (a.startsWith('--name=')) {
            name = a.split('=')[1] || "";
        }
    }
    return { name };
}

main().catch((e) => {
    console.error(e);
    process.exit(1);
});
