{"id": "TYPE-Genesis", "name": "Genesis", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$comment": "This schema defines all genesis schemas used throughout the Toolproof ecosystem. The genesis schemas themselves are defined in $defs.<ResourceType>.extractionSchema. The build process (via extractSchemas.js) extracts these schemas and writes them to a separate file (src/schemas/Genesis.json). The reason for this indirection is to have all these schema envelopes validate positively against the schema at $defs/TypeData, effectively making them ResourceTypes, which are first-class citizens in the Toolproof ecosystem.", "$id": "https://schemas.toolproof.com/v0/Genesis.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "$defs": {"Identifiable": {"id": "TYPE-Identifiable", "name": "Identifiable", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Identifiable", "type": "object", "required": ["id"]}}, "Name": {"id": "TYPE-Name", "name": "Name", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Name", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["name"], "properties": {"name": {"type": "string", "minLength": 1, "pattern": "^(?:[A-Z].*|[a-z]+/[a-z0-9.+-]+)$", "$comment": "Validation is intercepted by an AI Agent that verifies the name against Toolproof's naming conventions.", "semanticValidation": "Ajv custom keyword to verify name."}}}}, "Description": {"id": "TYPE-Description", "name": "Description", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Description", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["description"], "properties": {"description": {"type": "string", "minLength": 1, "$comment": "Validation is intercepted by an AI Agent that verifies the description against Toolproof's documentation standards.", "semanticValidation": "Ajv custom keyword to verify description."}}}}, "Documented": {"id": "TYPE-Documented", "name": "Documented", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Documented", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Name"}, {"$ref": "#/$defs/Description"}]}}, "IdentifiableDocumented": {"id": "TYPE-IdentifiableDocumented", "name": "IdentifiableDocumented", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentifiableDocumented", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/Documented"}]}}, "Path": {"id": "TYPE-Path", "name": "Path", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Path", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["path"], "properties": {"path": {"type": "string"}}}}, "Uri": {"id": "TYPE-<PERSON>ri", "name": "<PERSON><PERSON>", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "<PERSON><PERSON>", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["uri"], "properties": {"uri": {"type": "string", "format": "uri"}}}}, "FormatId": {"id": "TYPE-FormatId", "name": "FormatId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "FormatId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^FORMAT-.+$"}}, "FormatBase": {"id": "TYPE-FormatBase", "name": "FormatBase", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "FormatBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"required": ["id"], "properties": {"id": {"$ref": "#/$defs/FormatId"}}}]}}, "FormatData": {"id": "TYPE-FormatData", "name": "FormatData", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "FormatData", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/FormatBase"}], "unevaluatedProperties": false}}, "FormatMeta": {"id": "TYPE-FormatMeta", "name": "FormatMeta", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "FormatMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/FormatBase"}, {"$ref": "#/$defs/Path"}], "unevaluatedProperties": false}}, "ExtractionSchema": {"id": "TYPE-ExtractionSchema", "name": "ExtractionSchema", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExtractionSchema", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["extractionSchema"], "properties": {"extractionSchema": {"$ref": "#/$defs/ExtractionSchemaValue"}}}}, "ExtractionSchemaValue": {"id": "TYPE-ExtractionSchemaValue", "name": "ExtractionSchemaValue", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExtractionSchemaValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["$schema", "type"], "properties": {"$id": {"type": "string", "format": "uri"}, "$anchor": {"type": "string", "pattern": "^[A-Za-z][A-Za-z0-9._-]*$"}, "$schema": {"const": "https://json-schema.org/draft/2020-12/schema"}, "type": {"const": "object"}, "properties": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object"}, "$defs": {"type": "object"}, "allOf": {"type": "array", "items": {"type": "object"}}, "additionalProperties": {"const": false}, "unevaluatedProperties": {"const": false}}, "allOf": [{"anyOf": [{"required": ["$id"]}, {"required": ["$anchor"]}, true]}, {"$ref": "#/$defs/IdentityProp"}, {"$ref": "#/$defs/MeritProp"}, {"oneOf": [{"required": ["additionalProperties"]}, {"required": ["unevaluatedProperties"]}]}], "unevaluatedProperties": false}}, "IdentityValue": {"id": "TYPE-IdentityValue", "name": "IdentityValue", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentityValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"required": ["type"], "properties": {"type": {"enum": ["string", "integer", "boolean"]}}}, {"required": ["enum"], "properties": {"enum": {"type": "array", "items": {"type": "string"}, "minItems": 1}}}]}}, "IdentityProp": {"id": "TYPE-IdentityProp", "name": "IdentityProp", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "IdentityProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["required", "properties"], "properties": {"required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "contains": {"const": "identity"}}, "properties": {"type": "object", "required": ["identity"], "properties": {"identity": {"$ref": "#/$defs/IdentityValue"}}}}}}, "MeritValue": {"id": "TYPE-MeritValue", "name": "MeritValue", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"required": ["type"], "properties": {"type": {"enum": ["number", "integer"]}}}, {"required": ["enum"], "properties": {"enum": {"type": "array", "items": {"type": "number"}, "minItems": 1}}}]}}, "MeritProp": {"id": "TYPE-MeritProp", "name": "MeritProp", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "MeritProp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"properties": {"type": "object", "properties": {"merit": {"$ref": "#/$defs/MeritValue"}}}}}}, "TypeId": {"id": "TYPE-TypeId", "name": "TypeId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "TypeId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^TYPE-.+$"}}, "TypeBase": {"id": "TYPE-TypeBase", "name": "TypeBase", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "TypeBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/IdentifiableDocumented"}, {"required": ["id", "formatId"], "properties": {"id": {"$ref": "#/$defs/TypeId"}, "formatId": {"$ref": "#/$defs/FormatId"}}}]}}, "TypeData": {"id": "TYPE-TypeData", "name": "TypeData", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "TypeData", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/TypeBase"}, {"$ref": "#/$defs/ExtractionSchema"}]}}, "TypeMeta": {"id": "TYPE-TypeMeta", "name": "TypeMeta", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "TypeMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/TypeBase"}, {"$ref": "#/$defs/Path"}, {"if": {"$comment": "If formatId is FORMAT-ApplicationJson, then uri must not be present, but if formatId is not FORMAT-ApplicationJson, then uri must be present. This is because resources of types with format FORMAT-ApplicationJson are self-contained and do not need an extractor.", "properties": {"formatId": {"const": "FORMAT-ApplicationJson"}}}, "then": {"not": {"$ref": "#/$defs/Uri"}}, "else": {"$ref": "#/$defs/Uri"}}], "unevaluatedProperties": false}}, "RoleId": {"id": "TYPE-RoleId", "name": "RoleId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^ROLE-.+$"}}, "RoleLiteral": {"id": "TYPE-RoleLiteral", "name": "RoleLiteral", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleLiteral", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"required": ["typeId"], "properties": {"typeId": {"$ref": "#/$defs/TypeId"}}}, {"$ref": "#/$defs/Documented"}]}}, "RoleMap": {"id": "TYPE-RoleMap", "name": "RoleMap", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/RoleId"}, "additionalProperties": {"$ref": "#/$defs/RoleLiteral"}}}, "RolesInner": {"id": "TYPE-RolesInner", "name": "RolesInner", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RolesInner", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["inputMap", "outputMap"], "properties": {"inputMap": {"$ref": "#/$defs/RoleMap"}, "outputMap": {"$ref": "#/$defs/RoleMap"}}, "unevaluatedProperties": false}}, "RolesOuter": {"id": "TYPE-RolesOuter", "name": "RolesOuter", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RolesOuter", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["roles"], "properties": {"roles": {"$ref": "#/$defs/RolesInner"}}}}, "Job": {"id": "TYPE-Job", "name": "Job", "description": "dummy-description", "formatId": "FORMAT-ApplicationJob", "extractionSchema": {"$anchor": "Job", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["identity"], "properties": {"identity": {"type": "string"}}, "allOf": [{"$ref": "#/$defs/Documented"}, {"$ref": "#/$defs/RolesOuter"}, {"$ref": "#/$defs/Uri"}], "unevaluatedProperties": false}}, "RoleBindingMap": {"id": "TYPE-RoleBindingMap", "name": "RoleBindingMap", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindingMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/RoleId"}, "additionalProperties": {"$ref": "#/$defs/ResourceId"}}}, "RoleBindingsInner": {"id": "TYPE-RoleBindingsInner", "name": "RoleBindingsInner", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindingsInner", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["inputBindingMap", "outputBindingMap"], "properties": {"inputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}, "outputBindingMap": {"$ref": "#/$defs/RoleBindingMap"}}, "unevaluatedProperties": false}}, "RoleBindingsOuter": {"id": "TYPE-RoleBindingsOuter", "name": "RoleBindingsOuter", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "RoleBindingsOuter", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["roleB<PERSON>ings"], "properties": {"roleBindings": {"$ref": "#/$defs/RoleBindingsInner"}}}}, "ExecutionId": {"id": "TYPE-ExecutionId", "name": "ExecutionId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ExecutionId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^EXECUTION-.+$"}}, "Execution": {"id": "TYPE-Execution", "name": "Execution", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Execution", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "required": ["id", "jobId"], "properties": {"id": {"$ref": "#/$defs/ExecutionId"}, "jobId": {"$ref": "#/$defs/ResourceId"}}}, {"$comment": "This will be overlayed at runtime to specify roleBindings corresponding to the roles of the underlying job.", "$ref": "#/$defs/RoleBindingsOuter"}]}}, "ConditionalWrapper": {"id": "TYPE-ConditionalWrapper", "name": "ConditionalWrapper", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ConditionalWrapper", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["when", "what"], "properties": {"when": {"$ref": "#/$defs/WorkStep"}, "what": {"$ref": "#/$defs/WorkStep"}}, "unevaluatedProperties": false}}, "StepKind": {"id": "TYPE-<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "<PERSON><PERSON><PERSON>", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["kind"], "properties": {"kind": {"type": "string", "enum": ["work", "branch", "while", "for"]}}}}, "WorkStepId": {"id": "TYPE-WorkStepId", "name": "WorkStepId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WORKSTEP-.+$"}}, "WorkStep": {"id": "TYPE-WorkStep", "name": "WorkStep", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/StepKind"}, {"type": "object", "required": ["id", "kind", "execution"], "properties": {"id": {"$ref": "#/$defs/WorkStepId"}, "kind": {"const": "work"}, "execution": {"$ref": "#/$defs/Execution"}}}]}}, "BranchStepId": {"id": "TYPE-BranchStepId", "name": "BranchStepId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "BranchStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^BRANCHSTEP-.+$"}}, "BranchStep": {"id": "TYPE-BranchStep", "name": "BranchStep", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "BranchStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/StepKind"}, {"type": "object", "required": ["id", "kind", "cases"], "properties": {"id": {"$ref": "#/$defs/BranchStepId"}, "kind": {"const": "branch"}, "cases": {"type": "array", "items": {"$ref": "#/$defs/ConditionalWrapper"}, "minItems": 1, "uniqueItems": true}}}]}}, "WhileStepId": {"id": "TYPE-WhileStepId", "name": "WhileStepId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WhileStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WHILESTEP-.+$"}}, "WhileStep": {"id": "TYPE-WhileStep", "name": "WhileStep", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WhileStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/StepKind"}, {"type": "object", "required": ["id", "kind", "case"], "properties": {"id": {"$ref": "#/$defs/WhileStepId"}, "kind": {"const": "while"}, "case": {"$ref": "#/$defs/ConditionalWrapper"}}}]}}, "ForStepId": {"id": "TYPE-ForStepId", "name": "ForStepId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ForStepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^FORSTEP-.+$"}}, "ForStep": {"id": "TYPE-ForStep", "name": "ForStep", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ForStep", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"$ref": "#/$defs/StepKind"}, {"type": "object", "required": ["id", "kind", "case"], "properties": {"id": {"$ref": "#/$defs/ForStepId"}, "kind": {"const": "for"}, "case": {"$ref": "#/$defs/ConditionalWrapper"}}}]}}, "StepId": {"id": "TYPE-StepId", "name": "StepId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "StepId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStepId"}, {"$ref": "#/$defs/BranchStepId"}, {"$ref": "#/$defs/WhileStepId"}, {"$ref": "#/$defs/ForStepId"}]}}, "Step": {"id": "TYPE-Step", "name": "Step", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Step", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "oneOf": [{"$ref": "#/$defs/WorkStep"}, {"$ref": "#/$defs/BranchStep"}, {"$ref": "#/$defs/WhileStep"}, {"$ref": "#/$defs/ForStep"}], "unevaluatedProperties": false}}, "WorkflowId": {"id": "TYPE-WorkflowId", "name": "WorkflowId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkflowId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WORKFLOW-.+$"}}, "Workflow": {"id": "TYPE-Workflow", "name": "Workflow", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Workflow", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"type": "object", "required": ["id", "steps"], "properties": {"id": {"$ref": "#/$defs/WorkflowId"}, "steps": {"type": "array", "items": {"$ref": "#/$defs/Step"}, "uniqueItems": true}}}], "unevaluatedProperties": false}}, "Timestamp": {"id": "TYPE-Timestamp", "name": "Timestamp", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "Timestamp", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time"}}}}, "JsonValue": {"id": "TYPE-JsonValue", "name": "JsonValue", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "JsonValue", "$schema": "https://json-schema.org/draft/2020-12/schema", "oneOf": [{"type": "null"}, {"type": "boolean"}, {"type": "number"}, {"type": "string"}, {"type": "array", "items": {"$ref": "#/$defs/JsonValue"}}, {"type": "object", "additionalProperties": {"$ref": "#/$defs/JsonValue"}}]}}, "ResourceId": {"id": "TYPE-ResourceId", "name": "ResourceId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "$comment": "", "pattern": "^RESOURCE-.+$"}}, "PendingRef": {"id": "TYPE-PendingRef", "name": "PendingRef", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "PendingRef", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["pendingRef"], "properties": {"pendingRef": {"$comment": "This points to a resource created at a previous step in the same workflow. This resource is not yet realized at the time of defining the workflow, but will be realized when the step using this resource is executed.The Engine resolves this pointer at runtime.", "$ref": "#/$defs/ResourceSocket"}}}}, "CanonicalRef": {"id": "TYPE-CanonicalRef", "name": "CanonicalRef", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "CanonicalRef", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["canonicalRef"], "properties": {"canonicalRef": {"$comment": "This points to the canonical resource with an identical contentHash as this one. If no such resource exists, this resource will itself be the canonical resource and point to itself (i.e. canonicalRef = id).", "$ref": "#/$defs/ResourceId"}}}}, "CreationContext": {"id": "TYPE-CreationContext", "name": "CreationContext", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "CreationContext", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["creationContext"], "properties": {"creationContext": {"$ref": "#/$defs/ResourceSocket"}}}}, "ResourceSocket": {"id": "TYPE-ResourceSocket", "name": "ResourceSocket", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceSocket", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["roleId", "executionId"], "properties": {"roleId": {"$ref": "#/$defs/RoleId"}, "executionId": {"$ref": "#/$defs/ExecutionId"}}}}, "ResourceKind": {"id": "TYPE-ResourceKind", "name": "ResourceKind", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceKind", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "required": ["kind"], "properties": {"kind": {"enum": ["potential-input", "potential-output", "realized"]}}}}, "ResourceBase": {"id": "TYPE-ResourceBase", "name": "ResourceBase", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"required": ["id"], "properties": {"id": {"$ref": "#/$defs/ResourceId"}}}, {"required": ["typeId"], "properties": {"typeId": {"$ref": "#/$defs/TypeId"}}}, {"$ref": "#/$defs/CreationContext"}]}}, "ResourcePotentialInput": {"id": "TYPE-ResourcePotentialInput", "name": "ResourcePotentialInput", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourcePotentialInput", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "potential-input"}}}, {"$ref": "#/$defs/PendingRef"}], "unevaluatedProperties": false}}, "ResourcePotentialOutput": {"id": "TYPE-ResourcePotentialOutput", "name": "ResourcePotentialOutput", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourcePotentialOutput", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "potential-output"}}}], "unevaluatedProperties": false}}, "ResourceMeta": {"id": "TYPE-ResourceMeta", "name": "ResourceMeta", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/CanonicalRef"}, {"$ref": "#/$defs/Timestamp"}], "unevaluatedProperties": false}}, "ResourceCanonicalMetaBase": {"id": "TYPE-ResourceCanonicalMetaBase", "name": "ResourceCanonicalMetaBase", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceCanonicalMetaBase", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceBase"}, {"$ref": "#/$defs/ResourceKind"}, {"required": ["kind"], "properties": {"kind": {"const": "realized"}}}, {"$ref": "#/$defs/Path"}, {"$ref": "#/$defs/Timestamp"}]}}, "ResourceCanonicalMeta": {"id": "TYPE-ResourceCanonicalMeta", "name": "ResourceCanonicalMeta", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceCanonicalMeta", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "$ref": "#/$defs/ResourceCanonicalMetaBase", "unevaluatedProperties": false}}, "ResourceData": {"id": "TYPE-ResourceData", "name": "ResourceData", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceData", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/ResourceCanonicalMetaBase"}, {"required": ["extractedData"], "properties": {"extractedData": {"$comment": "This will be overlayed at runtime to match the data structure of the underlying type's extractionSchema. At compile time, we guarantee it has an identity property.", "type": "object", "required": ["identity"], "properties": {"identity": {"type": ["string", "integer", "boolean"]}}, "additionalProperties": {"$ref": "#/$defs/JsonValue"}}}}], "unevaluatedProperties": false}}, "ResourceMap": {"id": "TYPE-ResourceMap", "name": "ResourceMap", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "ResourceMap", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "propertyNames": {"$ref": "#/$defs/ExecutionId"}, "additionalProperties": {"type": "object", "propertyNames": {"$ref": "#/$defs/RoleId"}, "additionalProperties": {"oneOf": [{"$ref": "#/$defs/ResourcePotentialInput"}, {"$ref": "#/$defs/ResourcePotentialOutput"}, {"$ref": "#/$defs/ResourceData"}]}}}}, "WorkflowSpecId": {"id": "TYPE-WorkflowSpecId", "name": "WorkflowSpecId", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkflowSpecId", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "string", "pattern": "^WORKFLOWSPEC-.+$"}}, "WorkflowSpec": {"id": "TYPE-WorkflowSpec", "name": "WorkflowSpec", "formatId": "FORMAT-ApplicationJson", "extractionSchema": {"$anchor": "WorkflowSpec", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "allOf": [{"$ref": "#/$defs/Identifiable"}, {"required": ["id", "workflow", "resourceMaps"], "properties": {"id": {"$ref": "#/$defs/WorkflowSpecId"}, "workflow": {"$ref": "#/$defs/Workflow"}, "resourceMaps": {"type": "array", "items": {"$ref": "#/$defs/ResourceMap"}, "uniqueItems": true}}}]}}}}}