// Auto-generated from JSON schemas. Do not edit.

export type CombinedEntry =
  | BranchStep
  | BranchStepId
  | CanonicalRef
  | ConditionalWrapper
  | CreationContext
  | Description
  | Documented
  | Execution
  | ExecutionId
  | ExtractionSchema
  | ExtractionSchemaValue
  | ForStep
  | ForStepId
  | FormatBase
  | FormatData
  | FormatId
  | FormatMeta
  | Identifiable
  | IdentifiableDocumented
  | IdentityProp
  | IdentityValue
  | Job
  | JsonValue
  | MeritProp
  | MeritValue
  | Name
  | Path
  | PendingRef
  | ResourceBase
  | ResourceCanonicalMeta
  | ResourceCanonicalMetaBase
  | ResourceData
  | ResourceId
  | ResourceKind
  | ResourceMap
  | ResourceMeta
  | ResourcePotentialInput
  | ResourcePotentialOutput
  | ResourceSocket
  | RoleBindingMap
  | RoleBindingsInner
  | RoleBindingsOuter1
  | RoleId
  | RoleLiteral
  | RoleMap
  | RolesInner
  | RolesOuter
  | Step
  | StepId
  | StepKind
  | Timestamp
  | TypeBase
  | TypeData
  | TypeId
  | TypeMeta
  | Uri
  | WhileStep
  | WhileStepId
  | WorkStep
  | WorkStepId
  | Workflow
  | WorkflowId
  | WorkflowSpec
  | WorkflowSpecId
  | GenesisJson
  | Job_Documented
  | Job_RolesOuter
  | Job_Uri
  | Job_Name
  | Job_Description
  | Job_RolesInner
  | Job_RoleMap
  | Job_RoleLiteral
  | Job_RoleId
  | Job_TypeId;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "BranchStep".
 */
export type BranchStep = Identifiable &
  StepKind & {
    /**
     * @minItems 1
     */
    cases: [ConditionalWrapper, ...ConditionalWrapper[]];
    id: BranchStepId;
    kind: "branch";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WorkStep".
 */
export type WorkStep = Identifiable &
  StepKind & {
    execution: Execution;
    id: WorkStepId;
    kind: "work";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Execution".
 */
export type Execution = Identifiable & {
  id: ExecutionId;
  jobId: ResourceId;
} & RoleBindingsOuter;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExecutionId".
 */
export type ExecutionId = `EXECUTION-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceId".
 */
export type ResourceId = `RESOURCE-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WorkStepId".
 */
export type WorkStepId = `WORKSTEP-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "BranchStepId".
 */
export type BranchStepId = `BRANCHSTEP-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleId".
 */
export type RoleId = `ROLE-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Documented".
 */
export type Documented = Name & Description;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExtractionSchemaValue".
 */
export type ExtractionSchemaValue = unknown &
  IdentityProp &
  MeritProp & {
} & {
    $id?: string;
    $schema: "https://json-schema.org/draft/2020-12/schema";
    $defs?: {
};
    type: "object";
    allOf?: {
}[];
    properties?: {
};
    additionalProperties?: false;
    unevaluatedProperties?: false;
    $anchor?: string;
} & {
    $id?: string;
    $schema: "https://json-schema.org/draft/2020-12/schema";
    $defs?: {
};
    type: "object";
    allOf?: {
}[];
    properties?: {
};
    additionalProperties?: false;
    unevaluatedProperties?: false;
    $anchor?: string;
} & {
    $id?: string;
    $schema: "https://json-schema.org/draft/2020-12/schema";
    $defs?: {
};
    type: "object";
    allOf?: {
}[];
    properties?: {
};
    additionalProperties?: false;
    unevaluatedProperties?: false;
    $anchor?: string;
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdentityValue".
 */
export type IdentityValue =
  | {
      type: "string" | "integer" | "boolean";
}
  | {
      /**
       * @minItems 1
       */
      enum: [string, ...string[]];
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "MeritValue".
 */
export type MeritValue =
  | {
      type: "number" | "integer";
}
  | {
      /**
       * @minItems 1
       */
      enum: [number, ...number[]];
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ForStep".
 */
export type ForStep = Identifiable &
  StepKind & {
    case: ConditionalWrapper;
    id: ForStepId;
    kind: "for";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ForStepId".
 */
export type ForStepId = `FORSTEP-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "FormatBase".
 */
export type FormatBase = IdentifiableDocumented & {
  id: FormatId;
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdentifiableDocumented".
 */
export type IdentifiableDocumented = Identifiable & Documented;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "FormatId".
 */
export type FormatId = `FORMAT-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "FormatData".
 */
export type FormatData = FormatBase;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "FormatMeta".
 */
export type FormatMeta = FormatBase & Path;
export type Job = Job_Documented &
  Job_RolesOuter &
  Job_Uri & {
    identity: string;
};
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "Documented".
 */
export type Job_Documented = Job_Name & Job_Description;
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RoleLiteral".
 */
export type Job_RoleLiteral = {
  typeId: Job_TypeId;
} & Job_Documented;
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "TypeId".
 */
export type Job_TypeId = string;
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RoleId".
 */
export type Job_RoleId = string;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "JsonValue".
 */
export type JsonValue =
  | null
  | boolean
  | number
  | string
  | JsonValue[]
  | {
      [k: string]: JsonValue;
    };
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceBase".
 */
export type ResourceBase = Identifiable & {
  id: ResourceId;
} & {
  typeId: TypeId;
} & CreationContext;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "TypeId".
 */
export type TypeId = `TYPE-${string}`;
export type ResourceCanonicalMeta = ResourceBase &
  ResourceKind & {
    kind: "realized";
} & Path &
  Timestamp;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceCanonicalMetaBase".
 */
export type ResourceCanonicalMetaBase = ResourceBase &
  ResourceKind & {
    kind: "realized";
} & Path &
  Timestamp;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceData".
 */
export type ResourceData = ResourceCanonicalMetaBase & {
  extractedData: {
    identity: string | number | boolean;
    [k: string]: JsonValue;
  };
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourcePotentialInput".
 */
export type ResourcePotentialInput = ResourceBase &
  ResourceKind & {
    kind: "potential-input";
} & PendingRef;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourcePotentialOutput".
 */
export type ResourcePotentialOutput = ResourceBase &
  ResourceKind & {
    kind: "potential-output";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceMeta".
 */
export type ResourceMeta = ResourceBase & CanonicalRef & Timestamp;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleLiteral".
 */
export type RoleLiteral = {
  typeId: TypeId;
} & Documented;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Step".
 */
export type Step = WorkStep | BranchStep | WhileStep | ForStep;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WhileStep".
 */
export type WhileStep = Identifiable &
  StepKind & {
    case: ConditionalWrapper;
    id: WhileStepId;
    kind: "while";
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WhileStepId".
 */
export type WhileStepId = `WHILESTEP-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StepId".
 */
export type StepId = WorkStepId | BranchStepId | WhileStepId | ForStepId;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "TypeBase".
 */
export type TypeBase = IdentifiableDocumented & {
  formatId: FormatId;
  id: TypeId;
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "TypeData".
 */
export type TypeData = TypeBase & ExtractionSchema;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "TypeMeta".
 */
export type TypeMeta = TypeBase &
  Path & {
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Workflow".
 */
export type Workflow = Identifiable & {
  id: WorkflowId;
  steps: Step[];
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WorkflowId".
 */
export type WorkflowId = `WORKFLOW-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WorkflowSpec".
 */
export type WorkflowSpec = Identifiable & {
  id: WorkflowSpecId;
  resourceMaps: ResourceMap[];
  workflow: Workflow;
};
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "WorkflowSpecId".
 */
export type WorkflowSpecId = `WORKFLOWSPEC-${string}`;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceCanonicalMeta".
 */
export type ResourceCanonicalMetaBase1 = ResourceBase &
  ResourceKind & {
    kind: "realized";
} & Path &
  Timestamp;

/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Identifiable".
 */
export interface Identifiable {
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "StepKind".
 */
export interface StepKind {
  kind: "work" | "branch" | "while" | "for";
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ConditionalWrapper".
 */
export interface ConditionalWrapper {
  what: WorkStep;
  when: WorkStep;
}
export interface RoleBindingsOuter {
  roleBindings: RoleBindingsInner;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindingsInner".
 */
export interface RoleBindingsInner {
  inputBindingMap: RoleBindingMap;
  outputBindingMap: RoleBindingMap;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindingMap".
 */
export type RoleBindingMap = Record<RoleId, ResourceId>;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "CanonicalRef".
 */
export interface CanonicalRef {
  canonicalRef: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "CreationContext".
 */
export interface CreationContext {
  creationContext: ResourceSocket;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceSocket".
 */
export interface ResourceSocket {
  executionId: ExecutionId;
  roleId: RoleId;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Description".
 */
export interface Description {
  description: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Name".
 */
export interface Name {
  name: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ExtractionSchema".
 */
export interface ExtractionSchema {
  extractionSchema: unknown;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "IdentityProp".
 */
export interface IdentityProp {
  properties: {
    identity: IdentityValue;
};
  required: string[];
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "MeritProp".
 */
export interface MeritProp {
  properties?: {
    merit?: MeritValue;
};
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Path".
 */
export interface Path {
  path: string;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "Name".
 */
export interface Job_Name {
  name: string;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "Description".
 */
export interface Job_Description {
  description: string;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RolesOuter".
 */
export interface Job_RolesOuter {
  roles: Job_RolesInner;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RolesInner".
 */
export interface Job_RolesInner {
  inputMap: Job_RoleMap;
  outputMap: Job_RoleMap;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RoleMap".
 */
export interface Job_RoleMap {
  [k: string]: Job_RoleLiteral;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "Uri".
 */
export interface Job_Uri {
  uri: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "PendingRef".
 */
export interface PendingRef {
  pendingRef: ResourceSocket1;
}
export interface ResourceSocket1 {
  executionId: ExecutionId;
  roleId: RoleId;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceKind".
 */
export interface ResourceKind {
  kind: "potential-input" | "potential-output" | "realized";
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Timestamp".
 */
export interface Timestamp {
  timestamp: string;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "ResourceMap".
 */
export type ResourceMap = Record<ExecutionId, Record<RoleId, ResourcePotential | ResourceData>>;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleBindingsOuter".
 */
export interface RoleBindingsOuter1 {
  roleBindings: RoleBindingsInner;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RoleMap".
 */
export type RoleMap = Record<RoleId, RoleLiteral>;
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RolesInner".
 */
export interface RolesInner {
  inputMap: RoleMap;
  outputMap: RoleMap;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "RolesOuter".
 */
export interface RolesOuter {
  roles: RolesInner;
}
/**
 * This interface was referenced by `GenesisJson`'s JSON-Schema
 * via the `definition` "Uri".
 */
export interface Uri {
  uri: string;
}
export interface GenesisJson {
}
