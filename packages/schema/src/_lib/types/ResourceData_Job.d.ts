// Auto-generated strict composite type. Do not edit.
export type ResourceData_Job = ResourceCanonicalMetaBase & {
  extractedData: Job;
};
export type ResourceCanonicalMetaBase = ResourceBase &
  ResourceKind & {
    kind: "realized";
  } & Path &
  Timestamp;
export type ResourceBase = Identifiable & {
  id: string;
} & {
  typeId: string;
} & CreationContext;
export type Job = Documented &
  RolesOuter &
  Uri & {
    identity: string;
  };
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "Documented".
 */
export type Documented = Name & Description;
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RoleLiteral".
 */
export type RoleLiteral = {
  /**
   * This interface was referenced by `undefined`'s J<PERSON><PERSON>-Schema
   * via the `definition` "TypeId".
   */
  typeId: string;
} & Documented;

export interface Identifiable {
}
export interface CreationContext {
  creationContext: ResourceSocket;
}
export interface ResourceSocket {
  executionId: string;
  roleId: string;
}
export interface ResourceKind {
  kind: "potential-input" | "potential-output" | "realized";
}
export interface Path {
  path: string;
}
export interface Timestamp {
  timestamp: string;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "Name".
 */
export interface Name {
  name: string;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "Description".
 */
export interface Description {
  description: string;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RolesOuter".
 */
export interface RolesOuter {
  roles: RolesInner;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RolesInner".
 */
export interface RolesInner {
  inputMap: RoleMap;
  outputMap: RoleMap;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "RoleMap".
 */
export interface RoleMap {
  [k: string]: RoleLiteral;
}
/**
 * This interface was referenced by `undefined`'s JSON-Schema
 * via the `definition` "Uri".
 */
export interface Uri {
  uri: string;
}
