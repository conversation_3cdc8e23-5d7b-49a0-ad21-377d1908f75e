import type { Job } from './types/types.js';
import type { ResourceData_Job } from './types/ResourceData_Job.js';


const Job: Job = {
    identity: 'identity',
    name: 'name',
    description: 'description',
    uri: 'uri',
    roles: {
        inputMap: {
            'ROLE-1234': {
                typeId: 'TYPE-1234',
                name: 'name',
                description: 'description'
            }
        },
        outputMap: {
            'ROLE-5678': {
                typeId: 'TYPE-5678',
                name: 'name',
                description: 'description'
            }
        }
    }
};

const jobAlpha = {
    identity: 'identity',
    name: 'name',
    description: 'description',
    uri: 'uri',
    roles: {
        inputMap: {
            'data-input': {
                typeId: 'typeId',
                name: 'name',
                description: 'description'
            }
        },
        outputMap: {
            'data-output': {
                typeId: 'typeId',
                name: 'name',
                description: 'description'
            }
        }
    }
};

const jobBeta = {
    identity: 'identity',
    name: 'name',
    description: 'description',
    uri: 'uri',
    roles: {
        inputMap: {
            'data-input': {
                typeId: 'typeId',
                name: 'name',
                description: 'description'
            }
        },
        /* outputMap: {
            'data-output': {
                typeId: 'typeId',
                name: 'name',
                description: 'description'
            }
        } */
    }
};


// This checks (no error expected); we're providing all required fields
const resourceDataJobAlpha: ResourceData_Job = {
    id: 'RESOURCE-1',
    typeId: 'TYPE-ID',
    creationContext: {
        roleId: 'ROLE-ID',
        executionId: 'EXECUTION-ID',
    },
    kind: 'realized',
    timestamp: 'timestamp',
    path: 'path',
    extractedData: jobAlpha
};

// @ts-expect-error Missing path property
// This SHOULD fail (missing path) once ResourceData_Job enforces Path strictly.
const resourceDataJobAlpha2: ResourceData_Job = {
    id: 'RESOURCE-3',
    typeId: 'TYPE-ID',
    creationContext: {
        roleId: 'ROLE-ID',
        executionId: 'EXECUTION-ID',
    },
    kind: 'realized',
    timestamp: 'timestamp',
    // path: 'path',
    extractedData: jobAlpha
};

// @ts-expect-error Missing extractedData property
// This should fail; we're missing extractedData
const resourceDataJobAlpha3: ResourceData_Job = {
    id: 'RESOURCE-2',
    typeId: 'TYPE-ID',
    creationContext: {
        roleId: 'ROLE-ID',
        executionId: 'EXECUTION-ID',
    },
    kind: 'realized',
    timestamp: 'timestamp',
    path: 'path',
    // extractedData: jobAlpha
};

// This SHOULD fail (missing roles.outputMap) once Job's roles is strictly enforced.
const resourceDataJobBeta: ResourceData_Job = {
    id: 'RESOURCE-4',
    typeId: 'TYPE-ID',
    creationContext: {
        roleId: 'ROLE-ID',
        executionId: 'EXECUTION-ID',
    },
    kind: 'realized',
    timestamp: 'timestamp',
    path: 'path',
    // @ts-expect-error Missing outputMap inside roles (Job requires outputMap)
    extractedData: jobBeta
};


