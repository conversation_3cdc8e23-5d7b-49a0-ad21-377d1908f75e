
export const CONSTANTS = {
    SCHEMA: { // ATTENTION: should be generated from GenesisSchema.$defs
        FormatData: 'FormatData',
        TypeData: 'TypeData',
        Execution: 'Execution',
        ResourceMap: 'ResourceMap',
    },
    STORAGE: {
        BUCKETS: {
            tp_archetypes: 'tp-archetypes',
            tp_resources: 'tp-resources',
        },
        COLLECTIONS: {
            archetypes: 'archetypes',
            resources: 'resources',
        },
        FILTER: {
            members: 'members',
            specials: 'specials',
        }
    },
    ARCHETYPES: {
        formats: 'formats' as const,
        types: 'types' as const,
    },
    ARCHETYPES_PSEUDO: {
        roles: 'roles' as const,
    },
    RESOURCES: {
        resources: 'resources' as const,
        jobs: 'jobs' as const,
    },
    STEP: {
        work: 'work',
        branch: 'branch',
        while: 'while',
        for: 'for',
    },
    WORKFLOW: {
        execution: 'execution' as const,
        workflow: 'workflow' as const,
        workflowSpec: 'workflowSpec' as const,
    },
    ENGINE: {
        GraphRunWorkflow: 'GraphRunWorkflow',
    },
    SPECIALS: {
        FORMAT_ApplicationPrimitive: 'FORMAT-ApplicationPrimitive',
        FORMAT_ApplicationJson: 'FORMAT-ApplicationJson',
        FORMAT_ApplicationJob: 'FORMAT-ApplicationJob',
        TYPE_Boolean: 'TYPE-Boolean',
        TYPE_Integer: 'TYPE-Integer',
        TYPE_Job: 'TYPE-Job',
        TYPE_WorkflowSpec: 'TYPE-WorkflowSpec',
        ROLE_BUILDER: 'ROLE-Builder',
        JOB_Engine: 'JOB-Engine',
        BOOLEAN_false: 'RESOURCE-c2kAldyzgNhLdF79p0Vt',
        BOOLEAN_true: 'RESOURCE-FHycY5TxwEBngKVhkv2j',
    },
    TESTING: {
        Integer_Zero: 'TYPE-Integer/3335e31095a13a9a2b0ea41ca7d92a458780cd5671dc0a440a72cc1b1c4f2c81',
    }
} as const;