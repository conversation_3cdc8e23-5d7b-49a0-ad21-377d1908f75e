{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "baseUrl": ".", "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "skipLibCheck": true, "isolatedModules": true, "outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "incremental": true, "composite": false}, "types": ["node"], "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}