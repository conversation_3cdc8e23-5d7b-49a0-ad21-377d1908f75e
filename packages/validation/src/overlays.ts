import type { <PERSON><PERSON>d<PERSON><PERSON>, Job<PERSON>son, ExecutionJson } from '@toolproof-npm/schema';
import type { Schema<PERSON>ike, AddPatch } from './types/types.js';
import { jsonPointerUnescape } from './utils.js';


// DOC: Build a dynamic overlay for ExecutionSchema so that inputBindingMap and outputBindingMap must match exactly the inputMap/outputMap of the referenced job.
export function generateExecutionSchemaOverlay(ExecutionSchema: SchemaLike, job: JobJson) {

    const overlay: any = JSON.parse(JSON.stringify(ExecutionSchema));

    if (!Array.isArray(overlay.allOf)) {
        throw new Error('ExecutionSchema missing required allOf array');
    }

    const requiredInputBindingKeys = Object.keys(job.roles.inputMap) as RoleIdJson[];
    const requiredOutputBindingKeys = Object.keys(job.roles.outputMap) as RoleIdJson[];

    const requiredInputProperties = requiredInputBindingKeys.reduce((acc, rid) => {
        acc[rid] = true;
        return acc;
    }, {} as Record<string, unknown>);

    const requiredOutputProperties = requiredOutputBindingKeys.reduce((acc, rid) => {
        acc[rid] = true;
        return acc;
    }, {} as Record<string, unknown>);

    const inputBindingMapSchema = {
        type: 'object',
        properties: requiredInputProperties,
        required: requiredInputBindingKeys,
        unevaluatedProperties: false,
    };

    const outputBindingMapSchema = {
        type: 'object',
        properties: requiredOutputProperties,
        required: requiredOutputBindingKeys,
        unevaluatedProperties: false,
    };

    const patch: Array<AddPatch> = [
        {
            op: 'add',
            path: '/allOf/-',
            value: {
                type: 'object',
                properties: {
                    roleBindings: {
                        type: 'object',
                        properties: {
                            inputBindingMap: inputBindingMapSchema,
                            outputBindingMap: outputBindingMapSchema,
                        },
                    },
                },
            },
        },
    ];

    applyJsonPatchAddOnly(overlay as Record<string, unknown>, patch);
    return overlay;
}

// DOC: Require that all combinations of input-binding RoleIds from the execution and the execution.id appear as properties in the active reseouceMap. Additional properties remain allowed by base schema.
export function generateResourceMapSchemaOverlay(ResourceMapSchema: SchemaLike, execution: ExecutionJson) {
    const overlay: any = JSON.parse(JSON.stringify(ResourceMapSchema));
    if (!Array.isArray(overlay.allOf)) overlay.allOf = [];

    const inputBindingMap = execution.roleBindings.inputBindingMap;
    const requiredRoleIds = Object.keys(inputBindingMap) as RoleIdJson[];

    // If no inputs are required, skip overlay additions.
    if (requiredRoleIds.length === 0) return overlay;

    // Overlay enforces presence of execution.id property containing an object with required roleId properties.
    const requiredInnerProps = requiredRoleIds.reduce((acc, rid) => {
        acc[rid] = true;
        return acc;
    }, {} as Record<string, unknown>);

    const patch: Array<AddPatch> = [
        {
            op: 'add',
            path: '/allOf/-',
            value: {
                type: 'object',
                properties: {
                    [execution.id]: {
                        type: 'object',
                        properties: requiredInnerProps,
                        required: requiredRoleIds,
                    }
                },
                required: [execution.id],
            },
        },
    ];

    applyJsonPatchAddOnly(overlay as Record<string, unknown>, patch);
    return overlay;
}

// Minimal JSON Patch applier supporting only the 'add' operation and appending to arrays via '/-'.
// This is sufficient for our overlay use-case and avoids adding a dependency.
function applyJsonPatchAddOnly(target: Record<string, unknown>, ops: AddPatch[]) {
    for (const op of ops) {
        if (op.op !== 'add') throw new Error(`Unsupported JSON Patch op: ${op.op}`);
        const segments = op.path.split('/').slice(1).map(jsonPointerUnescape);
        if (segments.length === 0) {
            throw new Error('JSON Patch add to root is not supported in this context');
        }
        const last = segments[segments.length - 1];
        const parentPath = segments.slice(0, -1);
        let parent: any = target;
        for (const seg of parentPath) {
            if (parent == null || (typeof parent !== 'object')) {
                throw new Error(`JSON Patch path not found: /${parentPath.join('/')}`);
            }
            parent = (parent as any)[seg];
        }
        if (last === '-') {
            if (!Array.isArray(parent)) {
                throw new Error(`JSON Patch append requires array at /${parentPath.join('/')}`);
            }
            parent.push(op.value);
        } else {
            // Support adding a property or inserting into an array at a numeric index
            if (Array.isArray(parent)) {
                const idx = last === '' ? NaN : Number(last);
                if (!Number.isInteger(idx) || idx < 0 || idx > parent.length) {
                    throw new Error(`Invalid array index for JSON Patch add: ${last}`);
                }
                parent.splice(idx, 0, op.value);
            } else if (parent && typeof parent === 'object') {
                (parent as any)[last] = op.value;
            } else {
                throw new Error(`JSON Patch path parent is not an object/array at /${parentPath.join('/')}`);
            }
        }
    }
}
