import type { ExtractionSchemaValueJson } from '@toolproof-npm/schema';
import type { SchemaLike } from './types/types.js';
import { GenesisSchema } from '@toolproof-npm/schema';
import { jsonPointerUnescape, normalizeDefsPointer } from './utils.js';
import Ajv2020, { _, KeywordCxt, SchemaObjCxt } from 'ajv/dist/2020.js';
import addFormats from 'ajv-formats';


// Module-level cached Ajv instances per validator to avoid repeated schema registration
let ajvGenesis: Ajv2020.Ajv2020 | null = null;
let ajvResource: Ajv2020.Ajv2020 | null = null;

// ESM/TS compatibility helpers for Ajv and ajv-formats default exports
function createAjv() {
    const ajv = new Ajv2020.default({ allErrors: true, strict: false, strictSchema: false, useDefaults: false, passContext: true, code: { source: true } });
    addFormats.default(ajv);
    return ajv;
}

type ValidatorWrapper = {
    validate: Ajv2020.ValidateFunction<unknown>;
    ajv: Ajv2020.Ajv2020;
};

// Cache compiled validators for subschemas under GenesisSchema
const cacheKeyValidatorCache = new Map<string, ValidatorWrapper>();
const schemaValidatorCache = new WeakMap<object, ValidatorWrapper>();

export function getAjvGenesis() {
    if (ajvGenesis) return ajvGenesis;
    const ajv = createAjv();
    // addUIKeyword(ajv);
    ajv.addSchema(GenesisSchema);
    ajvGenesis = ajv;
    // new Ajv instance => clear validator cache tied to it
    cacheKeyValidatorCache.clear();
    return ajvGenesis;
}

// Compile (and cache) a validator for a provided schema object.
// Use this when you want to overlay a subschema before compiling.
// Optionally provide cacheKey to reuse compiled validators across structurally-equal schema objects.
export function getGenesisValidator(schema: SchemaLike, cacheKey?: string) {
    const ajv = getAjvGenesis();

    // 1) Cache by object identity
    if (schema && typeof schema === 'object') {
        const cached = schemaValidatorCache.get(schema as object);
        if (cached) {
            return cached;
        }
    }

    // 2) Optional named cache entry
    if (cacheKey) {
        const named = cacheKeyValidatorCache.get(cacheKey);
        if (named) {
            return named;
        }
    }

    const validate = ajv.compile(schema);

    const entry = { validate, ajv };
    if (cacheKey) cacheKeyValidatorCache.set(cacheKey, entry);
    if (schema && typeof schema === 'object') schemaValidatorCache.set(schema as object, entry);

    return entry;
}

export function getAjvResource() {
    if (ajvResource) return ajvResource;
    const ajv = createAjv();
    ajvResource = ajv;
    return ajvResource;
}

export function getResourceValidator(extractionSchemaValue: ExtractionSchemaValueJson) {
    const ajv = getAjvResource();
    return ajv.compile(extractionSchemaValue);
}

// Return the subschema JSON addressed by defsPointer.
// Pointer forms accepted: "FormatData", "#/$defs/FormatData", "/$defs/FormatData", "$defs/FormatData", "#"
// By default returns a deep-cloned object to avoid callers mutating the cached schema.
export function getGenesisSubschema(defsPointer: string, clone: boolean = true): SchemaLike {
    if (defsPointer == null || defsPointer === '') {
        throw new Error('A JSON Pointer must be provided. Use "#" for the top-level schema or one of: "FormatData", "#/$defs/FormatData", "/$defs/FormatData", "$defs/FormatData".');
    }

    const root = GenesisSchema as unknown as Record<string, any>;
    const pointer = normalizeDefsPointer(defsPointer);

    let node: any = root;
    if (pointer !== '#') {
        if (!pointer.startsWith('#/')) {
            throw new Error(`Invalid pointer: ${pointer}`);
        }
        const segments = pointer.slice(2).split('/').map(jsonPointerUnescape);
        for (const seg of segments) {
            if (node == null || typeof node !== 'object' || !(seg in node)) {
                throw new Error(`Pointer not found: ${pointer}`);
            }
            node = node[seg];
        }
    }

    return clone ? JSON.parse(JSON.stringify(node)) : node;
}

export function getGenesisRef(defsPointer: string): string {
    if (defsPointer == null || defsPointer === '') {
        throw new Error('A JSON Pointer must be provided. Use "#" for the top-level schema or one of: "FormatData", "#/$defs/FormatData", "/$defs/FormatData", "$defs/FormatData".');
    }

    const rootId = (GenesisSchema as any)?.$id ?? 'GenesisSchema';
    const pointer = normalizeDefsPointer(defsPointer);
    return pointer === '#' ? rootId : `${rootId}${pointer}`;
}






