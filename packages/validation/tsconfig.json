{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "emitDeclarationOnly": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "skipLibCheck": true, "isolatedModules": true, "resolveJsonModule": true, "incremental": true, "composite": false, "lib": ["ES2022"], "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "stashed", "**/*.test.ts", "**/*.spec.ts"]}