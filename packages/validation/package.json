{"name": "@toolproof-npm/validation", "version": "0.1.12", "description": "JSON schema validator utilities for ToolProof", "keywords": ["toolproof", "validator", "json-schema", "ajv"], "author": "ToolProof Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ToolProof/core.git", "directory": "packages/validation"}, "homepage": "https://github.com/ToolProof/core#readme", "bugs": {"url": "https://github.com/ToolProof/core/issues"}, "type": "module", "main": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "types": "./dist/index.d.ts", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "files": ["dist", "README.md"], "packageManager": "pnpm@10.15.0", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "start": "node ./dist/index.js", "generateStandalone": "node ./dist/scripts/generateStandalone.js && node ./dist/scripts/formatStandalone.js", "formatStandalone": "node ./dist/scripts/formatStandalone.js", "testStandalone": "node --loader ts-node/esm src/scripts/testStandalone.ts"}, "dependencies": {"@toolproof-npm/shared": "^0.1.18", "@toolproof-npm/schema": "^0.1.20", "ajv": "^8.17.1", "ajv-formats": "^3.0.1"}, "devDependencies": {"@types/node": "^20.19.11", "prettier": "^3.6.2", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.9.3"}}