import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}
export default config
console % pnpm run dev

> @toolproof/console@1.0.0 dev /Users/<USER>/Desktop/Development/project-clients/web/python/toolproof_code/core/packages/console
> next dev

  ▲ Next.js 14.2.33
  - Local:        http://localhost:3000
  - Environments: .env.local, .env

 ✓ Starting...
 ✓ Ready in 760ms
 ○ Compiling /builders ...
 ⨯ ./src/_lib/setup/globals.css.webpack[javascript/auto]!=!../../node_modules/.pnpm/next@14.2.33_@opentelemetry+api@1.9.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/.pnpm/next@14.2.33_@opentelemetry+api@1.9.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/_lib/setup/globals.css
ReferenceError: module is not defined in ES module scope
This file is being treated as an ES module because it has a '.js' file extension and '/Users/<USER>/Desktop/Development/project-clients/web/python/toolproof_code/core/packages/console/package.json' contains "type": "module". To treat it as a CommonJS script, rename it to use the '.cjs' file extension.
Import trace for requested module:
./src/_lib/setup/globals.css.webpack[javascript/auto]!=!../../node_modules/.pnpm/next@14.2.33_@opentelemetry+api@1.9.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/.pnpm/next@14.2.33_@opentelemetry+api@1.9.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/_lib/setup/globals.css
./src/_lib/setup/globals.css
 ⨯ ./src/_lib/setup/globals.css.webpack[javascript/auto]!=!../../node_modules/.pnpm/next@14.2.33_@opentelemetry+api@1.9.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/.pnpm/next@14.2.33_@opentelemetry+api@1.9.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/_lib/setup/globals.css
ReferenceError: module is not defined in ES module scope
This file is being treated as an ES module because it has a '.js' file extension and '/Users/<USER>/Desktop/Development/project-clients/web/python/toolproof_code/core/packages/console/package.json' contains "type": "module". To treat it as a CommonJS script, rename it to use the '.cjs' file extension.
Import trace for requested module:
./src/_lib/setup/globals.css.webpack[javascript/auto]!=!../../node_modules/.pnpm/next@14.2.33_@opentelemetry+api@1.9.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!../../node_modules/.pnpm/next@14.2.33_@opentelemetry+api@1.9.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/_lib/setup/globals.css
./src/_lib/setup/globals.css
 GET /builders 500 in 10741ms