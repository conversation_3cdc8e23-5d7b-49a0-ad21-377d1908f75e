{"name": "@toolproof/console", "version": "1.0.0", "description": "Web console interface for ToolProof workflow management", "keywords": ["toolproof", "console", "web", "nextjs", "workflow"], "author": "ToolProof Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ToolProof/core.git", "directory": "packages/console"}, "homepage": "https://github.com/ToolProof/core#readme", "bugs": {"url": "https://github.com/ToolProof/core/issues"}, "private": true, "type": "module", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "files": [".next", "public", "src"], "packageManager": "pnpm@10.15.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "clean": "rimraf .next out"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@google-cloud/storage": "^7.16.0", "@heroicons/react": "^2.0.18", "@langchain/core": "^0.3.55", "@langchain/langgraph": "^0.2.72", "@langchain/langgraph-sdk": "^0.0.74", "@pinecone-database/pinecone": "^2.0.1", "@reduxjs/toolkit": "^2.0.1", "@toolproof-npm/shared": "^0.1.15", "@toolproof-npm/schema": "^0.1.13", "@toolproof-npm/validation": "^0.1.4", "@types/react": "18.3.5", "@types/react-dom": "18.3.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "autoprefixer": "10.4.15", "dotenv": "^16.4.7", "eslint": "8.48.0", "eslint-config-next": "13.4.19", "firebase": "^10.13.1", "json-schema-to-ts": "^3.1.1", "next": "^14.1.4", "next-auth": "^4.23.1", "openid-client": "^5.7.1", "postcss": "8.4.29", "react": "18.2.0", "react-dom": "18.2.0", "react-firebase-hooks": "^5.1.1", "react-force-graph-3d": "^1.26.1", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-redux": "^9.1.0", "styled-jsx": "^5.1.1", "tailwindcss": "3.3.3", "three": "^0.177.0", "typescript": "^5.9.3", "uuid": "^11.1.0", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.3"}, "devDependencies": {"@types/three": "^0.177.0", "@typescript-eslint/eslint-plugin": "^6.6.0", "@typescript-eslint/parser": "^6.6.0", "json-schema-to-typescript": "^15.0.4", "rimraf": "^6.0.1", "ts-node": "^10.9.2"}}