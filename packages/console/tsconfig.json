{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noImplicitOverride": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": false, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/builders/*": ["./src/components/builders/*"], "@/explorer/*": ["./src/components/explorer/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "../shared/constants.ts", "src/components/explorer/worlds/ecosystem/EcosystemWorld.tsx"], "exclude": ["node_modules", "dist", ".next", "out"]}