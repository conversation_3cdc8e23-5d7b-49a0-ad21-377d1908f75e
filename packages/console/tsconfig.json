{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noImplicitOverride": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/builders/*": ["./src/components/builders/*"], "@/explorer/*": ["./src/components/explorer/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "../shared/constants.ts", "src/components/explorer/worlds/ecosystem/EcosystemWorld.tx"], "exclude": ["node_modules"]}