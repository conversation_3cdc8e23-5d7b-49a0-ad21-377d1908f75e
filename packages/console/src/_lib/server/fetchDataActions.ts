'use server';

import type {
  TypeId<PERSON>son,
} from '@toolproof-npm/schema';
import type { ArchetypeConst, FilterConst, ArchetypeMeta, ArchetypeData, ArchetypeMetaMap, ArchetypeDataMap, ResourceMetaMap, ResourceDataMap } from '@toolproof-npm/shared/types';
import { listArchetypesMeta as _listArchetypesMeta, listResourcesMeta as _listResourcesMeta, listArchetypesData as _listArchetypesData, listResourcesData as _listResourcesData } from '@toolproof-npm/shared';

// Thin server actions that wrap the canonical server-side jobs

export async function saListArchetypesMeta<T extends ArchetypeMeta>(
  groupKey: ArchetypeConst,
  filterConfig: Record<FilterConst, boolean>
): Promise<ArchetypeMetaMap<T>> {
  return _listArchetypesMeta<T>(groupKey, filterConfig);
}

export async function saListArchetypesData<T extends ArchetypeData>(
  groupKey: ArchetypeConst,
  filterConfig: Record<FilterConst, boolean>
): Promise<ArchetypeDataMap<T>> {
  return _listArchetypesData<T>(groupKey, filterConfig) as Promise<ArchetypeDataMap<T>>;
}

// Mirrors the conditional typing of the underlying job while staying ergonomic to call
// New simplified resources meta: only Types -> Resources
export async function saListResourcesMeta(
  typeIds: TypeIdJson[]
): Promise<ResourceMetaMap> {
  return _listResourcesMeta(typeIds);
}

// Data listing from buckets, keyed by type/signature ids depending on bucket
// New simplified resources data: only Types -> Resource files
export async function saListResourcesData(
  typeIds: TypeIdJson[]
): Promise<ResourceDataMap> {
  return _listResourcesData(typeIds);
}
