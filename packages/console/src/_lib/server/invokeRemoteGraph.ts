import dotenv from 'dotenv';
dotenv.config();
import type { WorkflowSpecJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { Client } from '@langchain/langgraph-sdk';
import { RemoteGraph } from '@langchain/langgraph/remote';
import { HumanMessage } from '@langchain/core/messages';


const urlLocal = `http://localhost:8123`;
const urlRemote = `https://engine-core-a7953b216e1d518b84f7f1f2cab2edfa.us.langgraph.app`;
const url = urlRemote; //process.env.URL || urlLocal;
const graphId = CONSTANTS.ENGINE.GraphRunWorkflow;
const client = new Client({
    apiUrl: url,
    apiKey: process.env.NEXT_PUBLIC_LANGCHAIN_API_KEY,
});
const remoteGraph = new RemoteGraph({ graphId, url, apiKey: process.env.NEXT_PUBLIC_LANGCHAIN_API_KEY });

export async function runRemoteGraph(workflowSpec: WorkflowSpecJson) {
    try {
        // Create a thread (or use an existing thread instead)
        const thread = await client.threads.create();
        // console.log('thread :', thread);
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 1800000); // 30 minutes
        // console.log('timeout :', timeout);

        try {
            // console.log('Invoking the graph')
            const result = await remoteGraph.stream({
                messages: [new HumanMessage('Graph is invoked')],
                dryModeManager: {
                    dryRunMode: false,
                    delay: 1000,
                    drySocketMode: true,
                },
                workflowSpec,
            }, {
                configurable: { thread_id: thread.thread_id },
                signal: controller.signal,
            });

            // console.log('threadId:', thread.thread_id);
            // console.log('result:', JSON.stringify(result.messages, null, 2));

            // return result;
            const data = [];
            for await (const event of result) {
                console.log('Event:', JSON.stringify(event, null, 2));
                data.push(event);
            }
            console.log('data:', JSON.stringify(data, null, 2));
            return data;

        } finally {
            clearTimeout(timeout);
            if (!controller.signal.aborted) {
                controller.abort();
            }
        }

    } catch (error) {
        console.error('Error invoking graph:', error);
    }

}
