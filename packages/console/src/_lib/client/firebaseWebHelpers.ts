import type { TypeId<PERSON><PERSON> } from '@toolproof-npm/schema';
import type { ArchetypeConst, FilterConst, ArchetypeMeta, ArchetypeData, ArchetypeMetaMap, ArchetypeDataMap, ResourceMetaMap, ResourceDataMap } from '@toolproof-npm/shared/types';
import { useEffect, useMemo, useState } from 'react';
import { saListResourcesMeta, saListArchetypesMeta, saListArchetypesData, saListResourcesData } from '@/_lib/server/fetchDataActions';


export const useArchetypesMeta = <T extends ArchetypeMeta>(
  groupKey: ArchetypeConst,
  filterConfig: Record<FilterConst, boolean>
) => {
  const [items, setItems] = useState<ArchetypeMetaMap<T>>({ members: [], specials: [] });
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  // stable key for switch to avoid object identity issues
  const switchKey = useMemo(
    () => `${filterConfig?.members ? '1' : '0'}|${filterConfig?.specials ? '1' : '0'}`,
    [filterConfig?.members, filterConfig?.specials]
  );

  useEffect(() => {
    let cancelled = false;
    const run = async () => {
      try {
        setLoading(true);
        setError(undefined);
        const result = await saListArchetypesMeta<T>(groupKey, filterConfig);
        if (!cancelled) setItems(result as ArchetypeMetaMap<T>);
      } catch (e) {
        if (!cancelled) setError(e as Error);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    run();
    return () => {
      cancelled = true;
    };
    // switchKey encodes filterConfig booleans; including the object triggers unnecessary reruns
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupKey, switchKey]);

  return { items, loading, error } as {
    items: ArchetypeMetaMap<T>;
    loading: boolean;
    error: Error | undefined;
  };
}

// Simplified resources meta hook: only Types -> Resources
export function useResourcesMeta(
  typeIds: TypeIdJson[],
  options?: { debug?: boolean }
): { items: ResourceMetaMap; loading: boolean; error: Error | undefined } {
  const debug = !!options?.debug;
  const typeIdsKey = useMemo(() => [...typeIds].sort().join('|'), [typeIds]);
  const [itemsById, setItemsById] = useState<ResourceMetaMap>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  useEffect(() => {
    let cancelled = false;
    const ids = typeIdsKey ? typeIdsKey.split('|').filter(Boolean) : [];
    if (debug) console.debug('[useResourcesMeta] fetch start', { typeIds: ids });
    const run = async () => {
      try {
        setLoading(ids.length > 0);
        setError(undefined);
        if (ids.length === 0) {
          setItemsById({});
          setLoading(false);
          return;
        }
        const result = await saListResourcesMeta(ids as TypeIdJson[]);
        if (!cancelled) setItemsById(result);
      } catch (e) {
        if (!cancelled) setError(e as Error);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    run();
    return () => { cancelled = true; };
  }, [typeIdsKey, debug]);

  return { items: itemsById, loading, error };
}

export const useArchetypesData = <T extends ArchetypeData>(
  groupKey: ArchetypeConst,
  filterConfig: Record<FilterConst, boolean>
) => {
  const [items, setItems] = useState<ArchetypeDataMap<T>>({ members: [], specials: [] });
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  // stable key for switch to avoid object identity issues
  const switchKey = useMemo(
    () => `${filterConfig?.members ? '1' : '0'}|${filterConfig?.specials ? '1' : '0'}`,
    [filterConfig?.members, filterConfig?.specials]
  );

  useEffect(() => {
    let cancelled = false;
    const run = async () => {
      try {
        setLoading(true);
        setError(undefined);
        const result = await saListArchetypesData<T>(groupKey, filterConfig);
        if (!cancelled) setItems(result as ArchetypeDataMap<T>);
      } catch (e) {
        if (!cancelled) setError(e as Error);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    run();
    return () => {
      cancelled = true;
    };
    // switchKey encodes filterConfig booleans; including the object triggers unnecessary reruns
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupKey, switchKey]);

  return { items, loading, error } as {
    items: ArchetypeDataMap<T>;
    loading: boolean;
    error: Error | undefined;
  };
}

// Simplified resources data hook: only Types -> Resources
export function useResourcesData(
  typeIds: TypeIdJson[],
  options?: { debug?: boolean }
): { items: ResourceDataMap; loading: boolean; error: Error | undefined } {
  const debug = !!options?.debug;
  const typeIdsKey = useMemo(() => [...typeIds].sort().join('|'), [typeIds]);
  const [itemsById, setItemsById] = useState<ResourceDataMap>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>(undefined);

  useEffect(() => {
    let cancelled = false;
    const ids = typeIdsKey ? typeIdsKey.split('|').filter(Boolean) : [];
    if (debug) console.debug('[useResourcesData] fetch start', { typeIds: ids });
    const run = async () => {
      try {
        setLoading(ids.length > 0);
        setError(undefined);
        if (ids.length === 0) {
          setItemsById({});
          setLoading(false);
          return;
        }
        const result = await saListResourcesData(ids as TypeIdJson[]);
        if (!cancelled) setItemsById(result);
      } catch (e) {
        if (!cancelled) setError(e as Error);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    run();
    return () => { cancelled = true; };
  }, [typeIdsKey, debug]);

  return { items: itemsById, loading, error };
}