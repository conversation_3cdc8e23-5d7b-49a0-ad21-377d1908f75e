'use client';

import React from 'react';

// Types matching the exposed schema (simplified for UI)
export type ColoredPaperSection = {
    // Description is now always an array of paragraphs
    description?: string[];
    sections?: Record<string, ColoredPaperSection>;
};

export type ColoredPaper = {
    identity: 'white' | 'yellow' | 'red' | 'green' | 'blue';
    sections: Record<string, ColoredPaperSection>;
};

type SectionProps = {
    name: string; // section key acts as the title
    section: ColoredPaperSection;
    depth?: number;
};

const Heading: React.FC<{ depth: number; children: React.ReactNode }> = ({ depth, children }) => {
    const Tag = (['h2', 'h3', 'h4', 'h5', 'h6'] as const)[Math.min(Math.max(depth - 1, 0), 4)];
    const size = [
        'text-2xl', // h2
        'text-xl', // h3
        'text-lg', // h4
        'text-base', // h5
        'text-base', // h6
    ][Math.min(Math.max(depth - 1, 0), 4)];
    return <Tag className={`${size} font-semibold mt-8 mb-2 text-black dark:text-white`}>{children}</Tag>;
};

const Section: React.FC<SectionProps> = ({ name, section, depth = 2 }) => {
    const { description, sections } = section;
    const paragraphs = description ?? [];

    const entries: Array<[string, ColoredPaperSection]> = sections
        ? Object.entries(sections)
        : [];

    return (
        <section className="mt-6">
            {name && <Heading depth={depth}>{name.replace(/-/g, ' ')}</Heading>}
            {paragraphs.length > 0 && (
                <div className="space-y-4 leading-relaxed">
                    {paragraphs.map((p, idx) => (
                        <p key={idx} className="text-black dark:text-white opacity-100">
                            {p}
                        </p>
                    ))}
                </div>
            )}
            {entries.length > 0 && (
                <div className="ml-0 md:ml-2 border-l-0 md:border-l md:border-slate-200 dark:md:border-slate-700 md:pl-4">
                    {entries.map(([childName, childSection]) => (
                        <Section key={childName} name={childName} section={childSection} depth={Math.min(depth + 1, 6)} />
                    ))}
                </div>
            )}
        </section>
    );
};

export const ColoredPaperView: React.FC<{ data: ColoredPaper; title?: string }> = ({ data, title }) => {
    const topEntries = Object.entries(data.sections ?? {});

    return (
        <article className="mx-auto max-w-3xl px-4 py-8 text-black dark:text-white bg-white dark:bg-neutral-900 rounded-lg shadow-sm isolate border border-slate-200 dark:border-neutral-800">
            {title && <h1 className="text-3xl font-bold tracking-tight mb-6 text-black dark:text-white">{title}</h1>}
            {topEntries.map(([name, section]) => (
                <Section key={name} name={name} section={section} />
            ))}
        </article>
    );
};

export default ColoredPaperView;
