'use client';

import { useState } from 'react';
import Whitepaper from './coloredPapers/Whitepaper.json';
import Yellowpaper from './coloredPapers/Yellowpaper.json';
import ColoredPaperView, { type ColoredPaper } from './views/ColoredPaperView';

type TabKey = 'whitepaper' | 'yellowpaper';

const tabs: { key: Tab<PERSON>ey; label: string }[] = [
    { key: 'whitepaper', label: 'Whitepaper' },
    { key: 'yellowpaper', label: 'Yellowpaper' },
];

export default function DocsEntry() {
    const [active, setActive] = useState<TabKey>('whitepaper');

    return (
        <div className="mx-auto max-w-5xl px-4 py-6">
            {/* Tabs */}
            <div className="mb-4 border-b border-slate-200 dark:border-neutral-800">
                <nav className="-mb-px flex gap-4" aria-label="Tabs">
                    {tabs.map((t) => {
                        const isActive = active === t.key;
                        return (
                            <button
                                key={t.key}
                                type="button"
                                onClick={() => setActive(t.key)}
                                className={
                                    `px-3 py-2 text-sm font-medium border-b-2 ` +
                                    (isActive
                                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                        : 'border-transparent text-slate-600 hover:text-slate-800 dark:text-slate-300 dark:hover:text-white')
                                }
                                aria-current={isActive ? 'page' : undefined}
                            >
                                {t.label}
                            </button>
                        );
                    })}
                </nav>
            </div>

            {/* Content */}
            <div>
                {active === 'whitepaper' && (
                    <ColoredPaperView data={Whitepaper as ColoredPaper} title="Whitepaper" />
                )}
                {active === 'yellowpaper' && (
                    <ColoredPaperView data={Yellowpaper as ColoredPaper} title="Yellowpaper" />
                )}
            </div>
        </div>
    );
}
