'use client';
import type { WorldInterface } from '@/explorer/worlds/_lib/types';
import Explorer from '@/explorer/Explorer';
import * as THREE from 'three';
import { useEffect, useRef } from 'react';


export default function ExplorerHost({ worldFactory, onWorldReady }: { worldFactory: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => WorldInterface, onWorldReady?: (world: WorldInterface) => void }) {
    const containerRef = useRef<HTMLDivElement>(null);
    const explorerRef = useRef<Explorer | null>(null);
    const worldReadyCbRef = useRef<typeof onWorldReady | undefined>(onWorldReady);
    // Keep latest callback without retriggering creation effect
    useEffect(() => { worldReadyCbRef.current = onWorldReady; }, [onWorldReady]);

    // DOC: Create and init the Explorer once when the container is ready
    useEffect(() => {
        if (!containerRef.current || explorerRef.current) return;
        // Create once per stable worldFactory (should be memoized upstream)
        const explorer = new Explorer(containerRef.current, worldFactory);
        explorerRef.current = explorer;
        try { worldReadyCbRef.current?.(explorer.getWorld()); } catch { /* ignore */ }
        explorer.init(); // Fire-and-forget; internal start handles render loop
        return () => {
            // Cleanup only if we actually created it here
            try { explorerRef.current?.dispose(); } finally { explorerRef.current = null; }
        };
    }, [worldFactory]);

    return (
        <div
            ref={containerRef}
            style={{ width: '100vw', height: '100vh', backgroundColor: 'orange' }}
        ></div>
    );

}
