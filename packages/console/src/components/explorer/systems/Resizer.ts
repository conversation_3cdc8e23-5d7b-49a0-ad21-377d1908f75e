import { Perspective<PERSON><PERSON><PERSON>, WebGLRenderer } from 'three';


class Resizer {
    private container: HTMLDivElement;
    private camera: PerspectiveCamera;
    private renderer: WebGLRenderer;
    private boundOnResize: () => void;

    private setSize = (container: HTMLDivElement, camera: PerspectiveC<PERSON>ra, renderer: WebGLRenderer) => {
        camera.aspect = container.clientWidth / container.clientHeight;
        camera.updateProjectionMatrix();

        renderer.setSize(container.clientWidth, container.clientHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
    };

    // Optional hook for subclasses/consumers
    onResize() { } // ATTENTION: let constructor take a callback instead

    constructor(container: HTMLDivElement, camera: PerspectiveCamera, renderer: WebGLRenderer) {
        this.container = container;
        this.camera = camera;
        this.renderer = renderer;

        // set initial size
        this.setSize(this.container, this.camera, this.renderer);

        this.boundOnResize = () => {
            this.setSize(this.container, this.camera, this.renderer);
            this.onResize();
        };

        window.addEventListener('resize', this.boundOnResize);
    }

    dispose() {
        window.removeEventListener('resize', this.boundOnResize);
    }
}

export { Resizer };