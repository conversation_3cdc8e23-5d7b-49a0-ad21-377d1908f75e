import type { MeshConfig, SphereConfig } from '@/explorer/worlds/_lib/types';

// Simple data shape for TestWorld dummy entities
export interface TestEntity {
  id: string;
  name?: string;
  description?: string;
  kind?: 'primary' | 'secondary';
  isDummy?: boolean;
}

export interface TestWorldConfig {
  meshConfig: MeshConfig<SphereConfig>;
  totalPrimary: number;
  totalSecondary: number;
}

export interface TestWorldData {
  entities: TestEntity[];
}
