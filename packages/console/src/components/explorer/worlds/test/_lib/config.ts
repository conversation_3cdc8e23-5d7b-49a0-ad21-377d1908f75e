import * as THREE from 'three';
import type { TestWorldConfig } from '@/explorer/worlds/test/_lib/types';

export function makeTestWorldConfig(): TestWorldConfig {
  return {
    meshConfig: {
      geometry: { kind: 'sphere', radius: 0.8, widthSegments: 16, heightSegments: 12, ringRadius: 8 },
      material: {
        color: new THREE.Color(0x4488ff),
        metalness: 0.25,
        roughness: 0.9,
        emissive: new THREE.Color(0x000000),
        side: THREE.FrontSide,
        transparent: false,
        opacity: 1.0,
        depthWrite: true,
        depthTest: true,
      },
    },
    totalPrimary: 12,
    totalSecondary: 6,
  };
}
