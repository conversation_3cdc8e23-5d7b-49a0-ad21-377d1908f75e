import type { ExplorerConfig } from '@/explorer/_lib/types';
import { BaseWorld } from '@/explorer/worlds/BaseWorld';
import { drawRingBasis, attachFlag } from '@/explorer/worlds/_lib/utils';
import type { EntityMeshMapOuter, MeshConfig, SphereConfig } from '@/explorer/worlds/_lib/types';
import type { TestEntity, TestWorldConfig, TestWorldData } from '@/explorer/worlds/test/_lib/types';
export { makeTestWorldConfig } from '@/explorer/worlds/test/_lib/config';
import * as THREE from 'three';

// Utility to synthesize dummy entities
export function makeDummyEntities(primary: number, secondary: number): TestEntity[] {
	const out: TestEntity[] = [];
	for (let i = 0; i < primary; i++) out.push({ id: `P-${i + 1}`, name: `Primary ${i + 1}`, description: 'Primary ring item', kind: 'primary', isDummy: i >= 3 });
	for (let i = 0; i < secondary; i++) out.push({ id: `S-${i + 1}`, name: `Secondary ${i + 1}`, description: 'Secondary ring item', kind: 'secondary', isDummy: i >= 2 });
	return out;
}

export class TestWorld extends BaseWorld<TestWorldConfig, TestWorldData> {
	private data: TestWorldData = { entities: [] };
	private entityMeshMap: EntityMeshMapOuter = {} as EntityMeshMapOuter;

	constructor(explorerConfig: ExplorerConfig<TestWorldConfig>, scene: THREE.Scene, renderer: THREE.WebGLRenderer) {
		super(explorerConfig, scene, renderer);
	}

	public updateData(payload: Partial<TestWorldData>): void {
		try {
			if (payload.entities) this.data.entities = payload.entities;
			this.drawScene();
		} catch {/* ignore */ }
	}

	drawScene(): void {
		// Cleanup previous test groups
		const remove: THREE.Object3D[] = [];
		this.root.traverse(obj => { if (obj.name === 'test-primary' || obj.name === 'test-secondary') remove.push(obj); });
		remove.forEach(o => o.parent?.remove(o));
		this.entityMeshMap = {} as EntityMeshMapOuter;

		const { meshConfig, totalPrimary, totalSecondary } = this.explorerConfig.world;
		// Partition entities
		const primaries = this.data.entities.filter(e => e.kind === 'primary');
		const secondaries = this.data.entities.filter(e => e.kind === 'secondary');

		// If not enough, pad with on-the-fly dummies (visual only)
		while (primaries.length < totalPrimary) primaries.push({ id: `P-DUMMY-${primaries.length + 1}`, name: 'Dummy P', description: 'Auto dummy', kind: 'primary', isDummy: true });
		while (secondaries.length < totalSecondary) secondaries.push({ id: `S-DUMMY-${secondaries.length + 1}`, name: 'Dummy S', description: 'Auto dummy', kind: 'secondary', isDummy: true });

		const center = new THREE.Vector3(0, 0, 0);
		const basis = { u: new THREE.Vector3(1, 0, 0), v: new THREE.Vector3(0, 0, 1) };

		// Primary ring (full circle)
		const primaryMap = drawRingBasis(this.root, 'test-primary', primaries, center, meshConfig, basis, {
			orientationMode: 'given',
			guideVisible: true,
			nameBy: e => e.name ?? e.id,
			decorateMesh: (m, e) => { if (e.isDummy) attachFlag(m, { height: 0.6, color: 0xff0000 }); m.material = (m.material as THREE.MeshStandardMaterial).clone(); (m.material as THREE.MeshStandardMaterial).emissive = new THREE.Color(e.isDummy ? 0x222222 : 0x0044ff); },
		});
		Object.assign(this.entityMeshMap, primaryMap);

		// Secondary ring raised above and smaller radius override
		const elevatedCenter = center.clone().add(new THREE.Vector3(0, meshConfig.geometry.radius * 2.5, 0));
		const secondaryMeshConfig: MeshConfig<SphereConfig> = { ...meshConfig, geometry: { ...meshConfig.geometry, ringRadius: meshConfig.geometry.ringRadius * 0.55 } };
		const secondaryMap = drawRingBasis(this.root, 'test-secondary', secondaries, elevatedCenter, secondaryMeshConfig, basis, {
			orientationMode: 'given',
			guideVisible: true,
			nameBy: e => e.name ?? e.id,
			decorateMesh: (m, e) => { if (e.isDummy) attachFlag(m, { height: 0.4, color: 0x00cc66 }); m.material = (m.material as THREE.MeshStandardMaterial).clone(); (m.material as THREE.MeshStandardMaterial).color = new THREE.Color(0xccaa44); },
		});
		Object.assign(this.entityMeshMap, secondaryMap);

		this.setInteractionEntityFilter(new Set(Object.keys(this.entityMeshMap)));
	}
}

// Note: makeTestWorldConfig is now exported from '@/explorer/worlds/test/_lib/config'.
