'use client';
import ExplorerHost from '@/explorer/ExplorerHost';
import type { WorldInterface } from '@/explorer/worlds/_lib/types';
import type { InteractionContext } from '@/explorer/interactors/_lib/types';
import { TransientSelector, PersistentSelector } from '@/explorer/interactors/selectors';
import { SwitchingInteractor } from '@/explorer/interactors/SwitchingInteractor';
import { makeExplorerConfig } from '@/explorer/_lib/config';
import { makeInteractorConfig } from '@/explorer/interactors/_lib/config';
import * as THREE from 'three';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { TestWorld, makeTestWorldConfig, makeDummyEntities } from './TestWorld';

export default function TestLoader() {
  // Stable predicate/selector/interactor
  const predicate = useCallback((obj: THREE.Object3D) => true, []);
  const activeSelector = useMemo(() => new PersistentSelector(), []);
  const interactorFactory = useCallback((ctx: InteractionContext) => new SwitchingInteractor(ctx), []);
  const interactorConfig = useMemo(() => makeInteractorConfig({ predicate, selector: activeSelector, interactorFactory }), [predicate, activeSelector, interactorFactory]);

  // Config: use simple test config
  const testConfig = useMemo(() => makeTestWorldConfig(), []);

  // World factory: no remote fetches, just create TestWorld
  const worldFactory = useCallback((scene: THREE.Scene, renderer: THREE.WebGLRenderer) => {
    const explorerConfig = makeExplorerConfig(interactorConfig, testConfig);
    return new TestWorld(explorerConfig, scene, renderer) as unknown as WorldInterface;
  }, [interactorConfig, testConfig]);

  // Synthesize dummy data and push once world is ready
  const worldRef = useRef<WorldInterface | null>(null);
  useEffect(() => {
    const w = worldRef.current as { updateData?: (p: { entities: Array<{ id: string; name?: string; description?: string; kind?: 'primary'|'secondary'; isDummy?: boolean }> }) => void } | null;
    if (!w?.updateData) return;
    const entities = makeDummyEntities(8, 4);
    w.updateData({ entities });
  }, []);

  return (
    <ExplorerHost
      worldFactory={worldFactory}
      onWorldReady={(w) => { worldRef.current = w; }}
    />
  );
}
