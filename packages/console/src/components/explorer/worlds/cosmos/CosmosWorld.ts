import type { <PERSON>at<PERSON><PERSON><PERSON><PERSON>, Type<PERSON><PERSON><PERSON><PERSON>, ResourceData<PERSON>son, WorkflowSpecJson } from '@toolproof-npm/schema';
import type { ArchetypeMeta, ArchetypeMetaMap, ResourceDataMap } from '@toolproof-npm/shared/types';
import type { CosmosConfig, CosmosWorldData } from '@/explorer/worlds/cosmos/_lib/types';
import type { ExplorerConfig } from '@/explorer/_lib/types';
import type { EntityMeshMapOuter } from '@/explorer/worlds/_lib/types';
import { BaseWorld } from '@/explorer/worlds/BaseWorld';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { drawRingBasis, findMeshById } from '@/explorer/worlds/_lib/utils';
import { computeFrameworkBaseRadius, getGridDetail, resolveGridSegments, getCosmosRoleTypeIdsForImplementation } from '@/explorer/worlds/cosmos/_lib/utils';
import { iterArchetypeMetaMap, drawSphereGrid } from '@/explorer/worlds/cosmos/_lib/utils';
import { StickySelectionHighlighter } from '@/explorer/worlds/cosmos/StickySelectionHighlighter';
import { JobProcessingAnimation, defaultJobAnimationConfig, EasingFunctions } from '@/components/explorer/worlds/cosmos/animations/JobProcessingAnimation';
import * as THREE from 'three';

// Helpers to resolve names/special ids from config with CONSTANTS fallbacks
const getNames = () => {
    return {
        formats: CONSTANTS.ARCHETYPES.formats,
        types: CONSTANTS.ARCHETYPES.types,
        resources: CONSTANTS.RESOURCES.resources,
        jobs: CONSTANTS.RESOURCES.jobs,
        roles: CONSTANTS.ARCHETYPES_PSEUDO.roles,
    };
};
const getSpecials = () => {
    return {
        TYPE_Job: CONSTANTS.SPECIALS.TYPE_Job,
        TYPE_Integer: CONSTANTS.SPECIALS.TYPE_Integer,
        TYPE_Boolean: CONSTANTS.SPECIALS.TYPE_Boolean,
        BOOLEAN_true: CONSTANTS.SPECIALS.BOOLEAN_true,
        BOOLEAN_false: CONSTANTS.SPECIALS.BOOLEAN_false,
        FORMAT_ApplicationPrimitive: CONSTANTS.SPECIALS.FORMAT_ApplicationPrimitive,
        JOB_Engine: CONSTANTS.SPECIALS.JOB_Engine,
    };
};
// Build a removable-name set per draw based on config
function buildRemovableSet(cfg: CosmosConfig): ReadonlySet<string> {
    const n = getNames();
    const s = getSpecials();
    const names: string[] = [
        n.formats,
        n.types,
        n.resources,
        n.jobs,
        `${n.jobs}-spokes`,
        n.roles,
        `${n.roles}-ring-guide`,
        'role-links',
        s.JOB_Engine,
    ];
    return new Set<string>(names);
}

export class CosmosWorld extends BaseWorld<CosmosConfig, CosmosWorldData> {
    private formatMetaMap: ArchetypeMetaMap<FormatMetaJson> = { members: [], specials: [] };
    private typeMetaMap: ArchetypeMetaMap<TypeMetaJson> = { members: [], specials: [] };
    private resourceDataMap: ResourceDataMap = {} as ResourceDataMap;
    private workflowSpec: WorkflowSpecJson | null = null;
    private entityMeshMap: EntityMeshMapOuter = {} as EntityMeshMapOuter;
    // Cache to avoid redundant redraws of roles per-frame
    private lastRolesJobId: string | null = null;
    private stickyHighlighter?: StickySelectionHighlighter;
    private jobAnimation?: JobProcessingAnimation;

    constructor(
        explorerConfig: ExplorerConfig<CosmosConfig>,
        scene: THREE.Scene,
        renderer: THREE.WebGLRenderer
    ) {
        super(explorerConfig, scene, renderer);
        // Initialize sticky highlighter for job-centric visuals
        try {
            this.stickyHighlighter = new StickySelectionHighlighter(this.scene, this);
        } catch { /* optional */ }
        // Initialize job processing animation (Engine at origin) with config
        const animConfig = explorerConfig.world.animations?.jobProcessing;
        this.jobAnimation = new JobProcessingAnimation(
            new THREE.Vector3(0, 0, 0),
            {
                pullInDuration: animConfig?.pullInDuration ?? defaultJobAnimationConfig.pullInDuration,
                pullOutDuration: animConfig?.pullOutDuration ?? defaultJobAnimationConfig.pullOutDuration,
                pauseBetweenJobs: animConfig?.pauseBetweenJobs ?? defaultJobAnimationConfig.pauseBetweenJobs,
                pauseInside: animConfig?.pauseInside ?? defaultJobAnimationConfig.pauseInside,
                pullInDepth: animConfig?.pullInDepth ?? defaultJobAnimationConfig.pullInDepth,
                pullInEasing: animConfig?.pullInEasing ?? EasingFunctions.easeInOutCubic,
                pullOutEasing: animConfig?.pullOutEasing ?? EasingFunctions.easeOutCubic,
                highlightJobColor: animConfig?.highlightJobColor,
                highlightEngineColor: animConfig?.highlightEngineColor,
                onJobStartAnimation: (jobId: string) => {
                    // Draw roles for the animating job
                    this.drawRoles(jobId);
                },
                onJobEndAnimation: (jobId: string) => {
                    // Clear roles when animation ends (roles are removed in drawRoles on next hover)
                    // The role lines are already hidden by typeConnectors visibility control
                },
                onToggleRoleVisibility: (jobId: string, visible: boolean) => {
                    // Toggle visibility of role meshes for the animating job
                    const n = getNames();
                    const roleMeshes = this.entityMeshMap[n.roles] || [];
                    roleMeshes.forEach(entry => {
                        for (const mesh of Object.values(entry)) {
                            if ((mesh as { mesh?: THREE.Mesh }).mesh) {
                                (mesh as { mesh?: THREE.Mesh }).mesh!.visible = visible;
                            }
                        }
                    });
                },
                onAnimationStop: () => {
                    // Re-enable all entity interactions when animation stops
                    this.updateInteractorEntityFilter();
                },
            }
        );
    }

    public updateData(payload: Partial<CosmosWorldData>): void {
        try {
            const { formatMetaMap, typeMetaMap, resourceDataMap, workflowSpec } = payload;

            let changed = false;
            if (formatMetaMap && formatMetaMap !== this.formatMetaMap) {
                this.formatMetaMap = formatMetaMap;
                changed = true;
            }
            if (typeMetaMap && typeMetaMap !== this.typeMetaMap) {
                this.typeMetaMap = typeMetaMap;
                changed = true;
            }
            if (resourceDataMap && resourceDataMap !== this.resourceDataMap) {
                this.resourceDataMap = resourceDataMap;
                changed = true;
            }
            if (workflowSpec !== undefined && workflowSpec !== this.workflowSpec) {
                this.workflowSpec = workflowSpec;
                changed = true;
            }
            console.log('cosmos.workflowSpec: ', JSON.stringify(this.workflowSpec, null, 2));
            if (!changed) return; // Nothing to do

            // Check if animation should be restarted after redraw
            const shouldRestartAnimation = this.jobAnimation?.isActive() ?? false;

            // Stop animation before redrawing scene
            this.jobAnimation?.stop();

            // Re-render scene when new data arrives
            this.drawScene();

            // Restart animation after scene is ready if it was running before
            if (shouldRestartAnimation && this.jobAnimation) {
                this.jobAnimation.start();
            }
        } catch (e) {
            console.error('Error in updateData/drawScene:', e);
            // Re-throw to see the actual error instead of silently ignoring
            throw e;
        }
    }

    // Public, lightweight updater: redraw role rings based on current hover job.
    // Clears prior role rings only when hover target changes.
    public override updateOnHover(): void {
        // Don't process hover interactions if animation is running
        if (this.jobAnimation?.isActive()) {
            // console.log('[CosmosWorld.updateOnHover] Animation is running, skipping hover processing');
            return;
        }

        // Update sticky highlighter with current selection
        try {
            const selected = this.getSelectedObject();
            this.stickyHighlighter?.onSelection(selected);
            this.stickyHighlighter?.tick();
        } catch { /* ignore */ }

        // Determine current hovered job id
        const hovered = this.getIntersectedObject();
        // console.log('[CosmosWorld.updateOnHover] Hovered object:', hovered?.name, hovered?.userData);
        const jobGroup = getNames().jobs;
        let cursor: THREE.Object3D | null = hovered;
        let jobId: string | null = null;
        while (cursor) {
            const ud = (cursor as unknown as { userData?: { entity?: string; id?: string } }).userData;
            if (ud?.entity === jobGroup && typeof ud.id === 'string') { jobId = ud.id; break; }
            cursor = cursor.parent;
        }

        // console.log('[CosmosWorld.updateOnHover] Detected jobId:', jobId, 'lastRolesJobId:', this.lastRolesJobId, 'currentAnimatingJobId:', this.jobAnimation?.getCurrentJobId());

        // If unchanged, do nothing
        if (jobId === this.lastRolesJobId) return;

        // Remove existing roles group(s)
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            if (
                child.name === getNames().roles ||
                child.name === `${getNames().roles}-ring-guide` ||
                child.name === 'role-links'
            ) toRemove.push(child);
        });
        toRemove.forEach(o => o.parent?.remove(o));
        // Also drop any roles index in entityMeshMap
        delete this.entityMeshMap[getNames().roles];

        // Update cache and, if we have a new hovered job, draw roles for it
        this.lastRolesJobId = jobId;
        if (jobId) {
            this.drawRoles();
        }
    }

    // Per-frame update for animations
    public update(_delta: number): void {
        // Update job processing animation
        this.jobAnimation?.update();
    }

    drawScene() {
        // console.log('=== Starting drawScene ===');
        const removable = buildRemovableSet(this.explorerConfig.world);
        const toRemove: THREE.Object3D[] = [];
        this.root.traverse(child => {
            if (removable.has(child.name)) {
                toRemove.push(child);
            }
        });
        toRemove.forEach(o => o.parent?.remove(o));
        this.entityMeshMap = {} as EntityMeshMapOuter;
        // console.log('Cleared entityMeshMap, about to draw components...');
        // this.drawFormats();
        try {
            this.drawEngine();
            // console.log('Finished drawEngine');
        } catch (e) {
            console.error('Error in drawEngine:', e);
        }

        try {
            this.drawJobs();
            // console.log('Finished drawJobs');
        } catch (e) {
            console.error('Error in drawJobs:', e);
        }

        try {
            this.drawTypes();
            // console.log('Finished drawTypes');
        } catch (e) {
            console.error('Error in drawTypes:', e);
        }

        try {
            // this.drawRoles();
            // console.log('Finished drawRoles');
        } catch (e) {
            console.error('Error in drawRoles:', e);
        }

        try {
            this.drawPrimitiveResources();
            // console.log('Finished drawPrimitiveResources');
        } catch (e) {
            console.error('Error in drawPrimitiveResources:', e);
        }
        const base = computeFrameworkBaseRadius(this.explorerConfig.world);
        const sphereRadius = base + (this.explorerConfig.world.sphere?.radiusOffset ?? 16);
        drawSphereGrid(this.root, {
            radius: sphereRadius,
            showSphereGuide: true,
            sphereStyle: { color: this.explorerConfig.world.sphere?.color, opacity: this.explorerConfig.world.sphere?.opacity },
            equatorStyle: {
                color: this.explorerConfig.world.lines.equator.material.color,
                opacity: this.explorerConfig.world.lines.equator.material.opacity,
                linewidth: this.explorerConfig.world.lines.equator.material.linewidth,
            },
            gridStyle: {
                color: this.explorerConfig.world.lines.sphereGrid.material.color,
                opacity: this.explorerConfig.world.lines.sphereGrid.material.opacity,
                linewidth: this.explorerConfig.world.lines.sphereGrid.material.linewidth,
            },
            gridDetail: getGridDetail(this.explorerConfig.world),
            cacheKey: 'cosmos',
        });
        // console.log('About to call updateInteractorEntityFilter...');
        this.updateInteractorEntityFilter();
        // console.log('=== Finished drawScene ===');
        // console.log('entityMeshMap:', JSON.stringify(flattenEntityMeshMap(this.entityMeshMap), null, 2));
    }

    private updateInteractorEntityFilter() {
        const groups: string[] = Object.keys(this.entityMeshMap);
        const n = getNames();
        // Ensure key interaction groups are present even if entityMeshMap not yet populated
        if (this.root.getObjectByName(n.resources)) groups.push(n.resources);
        if (this.root.getObjectByName(n.jobs)) groups.push(n.jobs);
        this.setInteractionEntityFilter(new Set(groups));
    }

    private meshEntriesFromEntityGroup(groupName: string): Array<{ id: string; mesh: THREE.Mesh }> {
        const res: Array<{ id: string; mesh: THREE.Mesh }> = [];
        const groups = this.entityMeshMap[groupName] ?? [];
        for (const rec of groups) for (const [id, info] of Object.entries(rec)) res.push({ id, mesh: (info as { mesh: THREE.Mesh }).mesh });
        return res;
    }

    private makeMaterialFromConfig(cfg: typeof this.explorerConfig.world.panels.resources.material | typeof this.explorerConfig.world.rings.jobs.mesh.material) {
        return new THREE.MeshStandardMaterial({
            color: cfg.color,
            metalness: cfg.metalness,
            roughness: cfg.roughness,
            emissive: cfg.emissive,
            side: cfg.side,
            transparent: cfg.transparent,
            opacity: cfg.opacity,
            depthWrite: cfg.depthWrite,
            depthTest: cfg.depthTest,
        });
    }

    private drawEngine(): void {
        const s = getSpecials();
        const engineCfg = this.explorerConfig.world.engine;
        if (!engineCfg?.isVisible) return;

        const groupName = s.JOB_Engine;
        const group = new THREE.Group();
        group.name = groupName;
        this.root.add(group);

        // Geometry handling (currently supports box; extendable for sphere)
        const geomCfg: typeof engineCfg.geometry = engineCfg.geometry; // relax type to allow future extension
        let geom: THREE.BufferGeometry;
        if (geomCfg && geomCfg.kind === 'box') {
            const w = geomCfg.width ?? 1;
            const h = geomCfg.height ?? 1;
            const d = geomCfg.depth ?? 1;
            const ws = geomCfg.widthSegments ?? 1;
            const hs = geomCfg.heightSegments ?? 1;
            const ds = geomCfg.depthSegments ?? 1;
            geom = new THREE.BoxGeometry(w, h, d, ws, hs, ds);
        } else {
            throw new Error('Unsupported geometry kind for engine');
        }

        const matCfg = engineCfg.material;
        const material = new THREE.MeshStandardMaterial({
            color: matCfg.color,
            metalness: matCfg.metalness,
            roughness: matCfg.roughness,
            emissive: matCfg.emissive,
            side: matCfg.side,
            transparent: matCfg.transparent,
            opacity: matCfg.opacity,
            depthWrite: matCfg.depthWrite,
            depthTest: matCfg.depthTest,
        });

        const mesh = new THREE.Mesh(geom, material);
        mesh.position.set(0, 0, 0);
        mesh.userData = { entity: groupName, id: s.JOB_Engine, name: 'Engine' };
        group.add(mesh);

        if (!this.entityMeshMap[groupName]) this.entityMeshMap[groupName] = [];
        const rec: Record<string, { mesh: THREE.Mesh }> = { Engine: { mesh } };
        (this.entityMeshMap[groupName] as Array<Record<string, { mesh: THREE.Mesh }>>).push(rec);

        // Register engine mesh with animation system for highlighting
        this.jobAnimation?.setEngineMesh(mesh);
    }

    private drawJobs(): void {
        const n = getNames();
        const s = getSpecials();
        // Use drawRingBasis to render Job resources in an XZ ring.
        const TYPE_Job_ID = s.TYPE_Job;
        // console.log('Drawing jobs - TYPE_Job_ID:', TYPE_Job_ID);
        // console.log('Available resource data keys:', Object.keys(this.resourceDataMap || {}));
        const items = (this.resourceDataMap?.[TYPE_Job_ID] ?? []) as ResourceDataJson[];
        // console.log('Job items found:', items.length);
        if (!items.length) return;

        const groupName = n.jobs;
        // console.log('Job groupName:', groupName);
        // Project resource items into the entity shape expected by drawRingBasis.
        const ringEntities = items.map(it => {
            const id = String(it.id ?? '');
            const exposed = it.extractedData as unknown as { identity?: unknown; name?: unknown; description?: unknown } | undefined;
            const name = (() => {
                const sem = exposed?.identity;
                if (typeof sem === 'string' || typeof sem === 'number') return String(sem);
                const nm = exposed?.name;
                if (typeof nm === 'string' || typeof nm === 'number') return String(nm);
                return id;
            })();
            const description = typeof exposed?.description === 'string' ? exposed.description : '';
            // Reset lastRolesJobId so hover/role state is clean for animation
            this.lastRolesJobId = null;

            return { id, name, description };
        });

        const center = new THREE.Vector3(0, 0, 0);
        const basis = { u: new THREE.Vector3(1, 0, 0), v: new THREE.Vector3(0, 0, 1) }; // XZ ring like types
        const ringCfg = this.explorerConfig.world.rings.jobs; // use configured radius directly

        const delta = drawRingBasis(
            this.root,
            groupName,
            ringEntities,
            center,
            ringCfg,
            basis,
            {
                orientationMode: 'given',
                guideConfig: {
                    ...ringCfg.ringGuide,
                    isVisible: false,
                }
            }
        );

        // Merge returned ring record(s) into entityMeshMap for interaction filtering.
        // console.log('Delta from drawRingBasis:', Object.keys(delta));
        for (const [key, arr] of Object.entries(delta)) {
            if (!this.entityMeshMap[key]) this.entityMeshMap[key] = [];
            this.entityMeshMap[key].push(...arr);
            // console.log(`Added ${arr.length} items to entityMeshMap[${key}]`);
        }
        // console.log('EntityMeshMap keys after drawJobs:', Object.keys(this.entityMeshMap));

        // Draw spokes from Engine to each Job
        const enginePos = new THREE.Vector3(0, 0, 0); // Engine is at the center
        const spokesGroup = new THREE.Group();
        spokesGroup.name = `${groupName}-spokes`;
        this.root.add(spokesGroup);

        // Create spoke material (using engineSpoke config)
        const spokeCfg = this.explorerConfig.world.lines.engineSpoke;
        const spokeMat = new THREE.LineBasicMaterial({
            color: spokeCfg.material.color,
            transparent: true,
            opacity: spokeCfg.material.opacity,
            depthWrite: spokeCfg.material.depthWrite,
            depthTest: spokeCfg.material.depthTest,
        });

        // Draw a spoke to each job and register with animation
        const jobMeshes = this.meshEntriesFromEntityGroup(groupName);

        // Clear previous animation jobs
        this.jobAnimation?.clearJobs();

        for (const { mesh } of jobMeshes) {
            const jobPos = mesh.position.clone();
            const spokeGeom = new THREE.BufferGeometry().setFromPoints([enginePos, jobPos]);
            const spoke = new THREE.Line(spokeGeom, spokeMat.clone());
            spoke.renderOrder = 9998;
            spokesGroup.add(spoke);

            // Register job with animation system
            this.jobAnimation?.addJob(mesh, spoke);
        }

        // Start the animation if enabled and we have jobs
        const animEnabled = this.explorerConfig.world.animations?.jobProcessing?.enabled ?? true;
        /* console.log('[CosmosWorld.drawJobs] Animation check:', {
            jobCount: jobMeshes.length,
            animEnabled,
            hasJobAnimation: !!this.jobAnimation,
            animConfig: this.explorerConfig.world.animations?.jobProcessing
        }); */
        if (jobMeshes.length > 0 && animEnabled) {
            // Delay animation start to ensure scene is fully settled
            setTimeout(() => {
                // console.log('[CosmosWorld.drawJobs] Starting animation after delay, isRunning before:', this.jobAnimation?.isActive());
                // Disable interaction with jobs and engine while animation runs, but allow types, roles, and resources
                this.setInteractionEntityFilter(new Set([getNames().types, getNames().roles, getNames().resources]));
                this.jobAnimation?.start();
                // console.log('[CosmosWorld.drawJobs] Animation start() called, isRunning after:', this.jobAnimation?.isActive());
            }, 100);
        }

        // Ensure interactor entity filters include newly drawn jobs when animation is disabled
        if (!animEnabled) {
            try {
                this.updateInteractorEntityFilter();
            } catch { /* ignore */ }
        }
    }

    private drawFormats(): void {
        const n = getNames();
        const s = getSpecials();
        // New behavior: draw only ApplicationPrimitive at the center; do not draw ApplicationJob or any ring of formats.
        const FORMAT_ApplicationPrimitive_ID = String(s.FORMAT_ApplicationPrimitive);
        let FORMAT_ApplicationPrimitive_Meta: ArchetypeMeta | undefined;
        for (const { value } of iterArchetypeMetaMap(this.formatMetaMap)) {
            const vmeta = value as unknown as ArchetypeMeta;
            if (vmeta.id === FORMAT_ApplicationPrimitive_ID) { FORMAT_ApplicationPrimitive_Meta = vmeta; break; }
        }
        if (!FORMAT_ApplicationPrimitive_Meta) {
            throw new Error('Missing required special: FORMAT-ApplicationPrimitive in formatMetaMap');
        }

        const groupName = n.formats;
        const group = new THREE.Group();
        group.name = groupName;
        this.root.add(group);

        // Try to respect material from config if present; otherwise use a sensible default
        type MaterialCfg = {
            color?: number;
            metalness?: number;
            roughness?: number;
            emissive?: number;
            side?: THREE.Side;
            transparent?: boolean;
            opacity?: number;
            depthWrite?: boolean;
            depthTest?: boolean;
        };
        type FormatCfgLike = { material?: MaterialCfg; geometry?: { kind?: string; radius?: number; widthSegments?: number; heightSegments?: number } };
        const cfgLike = this.explorerConfig.world.rings.formats as unknown as FormatCfgLike;
        const matCfg = cfgLike?.material;
        const radius = (cfgLike?.geometry?.kind === 'sphere' && typeof cfgLike.geometry.radius === 'number') ? cfgLike.geometry.radius : 2.0;
        const widthSegments = (cfgLike?.geometry?.kind === 'sphere' && typeof cfgLike.geometry.widthSegments === 'number') ? cfgLike.geometry.widthSegments : 32;
        const heightSegments = (cfgLike?.geometry?.kind === 'sphere' && typeof cfgLike.geometry.heightSegments === 'number') ? cfgLike.geometry.heightSegments : 32;
        const material = matCfg
            ? new THREE.MeshStandardMaterial({
                color: matCfg.color,
                metalness: matCfg.metalness,
                roughness: matCfg.roughness,
                emissive: matCfg.emissive,
                side: matCfg.side,
                transparent: matCfg.transparent,
                opacity: matCfg.opacity,
                depthWrite: matCfg.depthWrite,
                depthTest: matCfg.depthTest,
            })
            : new THREE.MeshStandardMaterial({ color: 0x8888ff, metalness: 0.2, roughness: 0.8 });

        const geom = new THREE.SphereGeometry(radius, widthSegments, heightSegments);
        const mesh = new THREE.Mesh(geom, material);
        mesh.position.set(0, 0, 0);
        mesh.userData = { entity: groupName, id: FORMAT_ApplicationPrimitive_Meta.id, name: (FORMAT_ApplicationPrimitive_Meta as unknown as { name?: string }).name };
        group.add(mesh);

        if (!this.entityMeshMap[groupName]) this.entityMeshMap[groupName] = [];
        const rec: Record<string, { mesh: THREE.Mesh }> = { [FORMAT_ApplicationPrimitive_Meta.id]: { mesh } };
        (this.entityMeshMap[groupName] as Array<Record<string, { mesh: THREE.Mesh }>>).push(rec);
    }

    private drawTypes() {
        const n = getNames();
        const s = getSpecials();
        const FORMAT_ApplicationPrimitive_ID = s.FORMAT_ApplicationPrimitive;
        const TYPE_Integer_ID = s.TYPE_Integer;
        const TYPE_Boolean_ID = s.TYPE_Boolean;

        // Collect all type metas
        const allTypes: TypeMetaJson[] = [];
        for (const { value } of iterArchetypeMetaMap(this.typeMetaMap)) {
            allTypes.push(value);
        }

        // Find fixed-position specials
        const intMeta = allTypes.find(t => t.id === TYPE_Integer_ID);
        const boolMeta = allTypes.find(t => t.id === TYPE_Boolean_ID);
        if (!intMeta) throw new Error('Missing required special: TYPE-Integer in typeMetaMap');
        if (!boolMeta) throw new Error('Missing required special: TYPE-Boolean in typeMetaMap');
        if (intMeta.formatId !== FORMAT_ApplicationPrimitive_ID) throw new Error('TYPE-Integer must compose FORMAT-ApplicationPrimitive');
        if (boolMeta.formatId !== FORMAT_ApplicationPrimitive_ID) throw new Error('TYPE-Boolean must compose FORMAT-ApplicationPrimitive');

        // Other types: everything except Integer/Boolean/Job
        const TYPE_Job_ID = String(s.TYPE_Job);
        const others = allTypes.filter(t => t.id !== TYPE_Integer_ID && t.id !== TYPE_Boolean_ID && t.id !== TYPE_Job_ID);
        // Stable order for determinism: by id
        others.sort((a, b) => String(a.id).localeCompare(String(b.id)));

        // Build final entities list: include all types
        const entities: ArchetypeMeta[] = [intMeta, boolMeta, ...others] as unknown as ArchetypeMeta[];

        const center = new THREE.Vector3(0, 0, 0);
        const u = new THREE.Vector3(1, 0, 0);
        const v = new THREE.Vector3(0, 0, 1);

        // Precompute angles: Integer at 0, Boolean at π, others spread evenly across the two half-arcs
        const angleMap = new Map<string, number>();
        angleMap.set(TYPE_Integer_ID, 0);
        angleMap.set(TYPE_Boolean_ID, Math.PI);
        const nOthers = others.length;
        if (nOthers > 0) {
            const gap = 0.12; // keep a clear gap around 0 and π to avoid overlap with fixed types
            const span = Math.max(Math.PI - 2 * gap, 0.001); // usable span per half-arc
            const firstHalfCount = Math.ceil(nOthers / 2); // [0+gap, π-gap]
            const secondHalfCount = nOthers - firstHalfCount; // [π+gap, 2π-gap]

            // Distribute on first half-arc
            if (firstHalfCount > 0) {
                for (let i = 0; i < firstHalfCount; i++) {
                    const t = (i + 0.5) / firstHalfCount; // center in each segment
                    const angle = gap + t * span; // in (0, π)
                    angleMap.set(others[i].id, angle);
                }
            }
            // Distribute on second half-arc
            if (secondHalfCount > 0) {
                for (let j = 0; j < secondHalfCount; j++) {
                    const t = (j + 0.5) / secondHalfCount;
                    const angle = Math.PI + gap + t * span; // in (π, 2π)
                    angleMap.set(others[firstHalfCount + j].id, angle);
                }
            }
        }

        const ringCfg = this.explorerConfig.world.rings.types;
        const guideConfig = this.explorerConfig.world.lines.ringGuide;

        const delta = drawRingBasis(
            this.root,
            n.types,
            entities,
            center,
            ringCfg,
            { u, v },
            {
                guideConfig,
                orientationMode: 'given',
                angleBy: (entity: ArchetypeMeta) => {
                    const angle = angleMap.get(entity.id);
                    if (angle == null) {
                        // As a fallback, evenly spread any unexpected entity using its index amongst total
                        // Note: drawRingBasis still passes (entity, i, count), but we compute stable angles ahead of time
                        return 0;
                    }
                    return angle;
                }
            }
        );
        for (const [key, arr] of Object.entries(delta)) {
            if (!this.entityMeshMap[key]) this.entityMeshMap[key] = [];
            this.entityMeshMap[key].push(...arr);
        }
    }

    // Expose role type IDs for a given Job id so StickySelectionHighlighter can draw links to integer types.
    // Delegates to cosmos-specific utility to keep world implementation clean.
    public getRoleTypeIdsForImplementation(jobId: string): { inputTypeIds: string[]; outputTypeIds: string[] } {
        return getCosmosRoleTypeIdsForImplementation(jobId, this.resourceDataMap, this.explorerConfig.world);
    }

    // Draw roles based on all Job resources: for each referenced type, build a vertical ring
    // with input roles in the upper semicircle and output roles in the lower semicircle.
    private drawRoles(forAnimationJobId?: string): void {
        // console.log('[CosmosWorld.drawRoles] Called with forAnimationJobId:', forAnimationJobId);
        const n = getNames();
        const s = getSpecials();
        // Draw roles for the currently hovered job OR the animating job
        let jobId: string | null = null;

        if (forAnimationJobId) {
            // Called for animation - use provided job ID
            jobId = forAnimationJobId;
            // console.log('[CosmosWorld.drawRoles] Animation path, clearing existing roles');
            // For animation, clear any existing role rings/links to start clean
            const toRemove: THREE.Object3D[] = [];
            this.root.traverse(child => {
                if (
                    child.name === n.roles ||
                    child.name === `${n.roles}-ring-guide` ||
                    child.name === 'role-links'
                ) toRemove.push(child);
            });
            toRemove.forEach(o => o.parent?.remove(o));
            delete this.entityMeshMap[n.roles];
        } else {
            // Called for hover - find hovered job
            // console.log('[CosmosWorld.drawRoles] Hover path, finding hovered job');
            const hovered = this.getIntersectedObject();
            // console.log('[CosmosWorld.drawRoles] Hovered object:', hovered?.name);
            if (!hovered) return;
            const jobGroup = n.jobs;
            let cursor: THREE.Object3D | null = hovered;
            while (cursor) {
                const ud = (cursor as unknown as { userData?: { entity?: string; id?: string } }).userData;
                if (ud?.entity === jobGroup && typeof ud.id === 'string') { jobId = ud.id; break; }
                cursor = cursor.parent;
            }

            // console.log('[CosmosWorld.drawRoles] Found hover jobId:', jobId);

            // Don't draw hover roles if this job is currently animating
            const currentAnimatingJobId = this.jobAnimation?.getCurrentJobId();
            // console.log('[CosmosWorld.drawRoles] Current animating jobId:', currentAnimatingJobId);
            if (currentAnimatingJobId && currentAnimatingJobId === jobId) {
                // console.log('[CosmosWorld.drawRoles] Skipping hover roles because job is animating');
                return;
            }
        }

        if (!jobId) {
            // console.log('[CosmosWorld.drawRoles] No jobId found, exiting');
            return;
        }

        // console.log('[CosmosWorld.drawRoles] Processing roles for jobId:', jobId);

        const TYPE_Job_ID = s.TYPE_Job;
        const jobItems = (this.resourceDataMap?.[TYPE_Job_ID] ?? []) as ResourceDataJson[];
        const item = jobItems.find(i => String(i.id) === jobId);
        if (!item) {
            return;
        }

        // Collect roles for this job grouped by target type
        type RoleEntry = { id: string; name: string; description: string; kind: 'input' | 'output'; typeId: string };
        const rolesByType = new Map<string, RoleEntry[]>();
        const exposed = item.extractedData as unknown as { roles?: { inputMap?: Record<string, { typeId: string; name?: string; description?: string }>; outputMap?: Record<string, { typeId: string; name?: string; description?: string }> } };
        const inputMap = exposed?.roles?.inputMap ?? {};
        const outputMap = exposed?.roles?.outputMap ?? {};
        for (const [roleId, info] of Object.entries(inputMap)) {
            if (!info?.typeId) continue;
            const arr = rolesByType.get(info.typeId) ?? [];
            arr.push({ id: roleId, name: String(info.name ?? roleId), description: String(info.description ?? ''), kind: 'input', typeId: info.typeId });
            rolesByType.set(info.typeId, arr);
        }
        for (const [roleId, info] of Object.entries(outputMap)) {
            if (!info?.typeId) continue;
            const arr = rolesByType.get(info.typeId) ?? [];
            arr.push({ id: roleId, name: String(info.name ?? roleId), description: String(info.description ?? ''), kind: 'output', typeId: info.typeId });
            rolesByType.set(info.typeId, arr);
        }
        if (rolesByType.size === 0) return;

        const groupName = n.roles;
        // Job mesh for link line source
        const jobMeshEntry = this.meshEntriesFromEntityGroup(n.jobs).find(e => e.id === jobId);
        const jobMesh = jobMeshEntry?.mesh;
        const jobPos = jobMesh?.position.clone();

        // Group to hold link lines (green for inputs, red for outputs)
        const linkGroup = new THREE.Group();
        linkGroup.name = 'role-links';
        this.root.add(linkGroup);

        // Array to store all role connector lines for animation
        const roleConnectors: THREE.Line[] = [];

        const makeLine = (from: THREE.Vector3, to: THREE.Vector3, color: number) => {
            const geom = new THREE.BufferGeometry().setFromPoints([from.clone(), to.clone()]);
            const mat = new THREE.LineBasicMaterial({
                color,
                transparent: true,
                opacity: 0.9,
                depthWrite: false,
                depthTest: false  // Changed to false to ensure connectors are always visible
            });
            const line = new THREE.Line(geom, mat);
            line.renderOrder = 10000; // Higher render order to draw on top
            line.frustumCulled = false; // Prevent frustum culling
            // console.log('makeLine: from', from.toArray(), 'to', to.toArray(), 'distance', from.distanceTo(to));
            return line;
        };
        for (const [typeId, entries] of rolesByType.entries()) {
            const typeMesh = this.meshEntriesFromEntityGroup(n.types).find(e => e.id === typeId)?.mesh;
            if (!typeMesh) {
                continue;
            }
            const center = typeMesh.position.clone();

            // Choose a local radius so roles don't intersect the type mesh
            typeMesh.geometry.computeBoundingSphere();
            const typeRadius = typeMesh.geometry.boundingSphere?.radius ?? 1;
            const localRadius = typeRadius * 2.2;

            const baseRolesCfg = this.explorerConfig.world.rings.roles ?? this.explorerConfig.world.rings.types;
            const ringCfg = { ...baseRolesCfg, ringRadius: localRadius } as typeof baseRolesCfg;

            // Basis: align ring to follow the curvature of the type ring.
            // Use tangent (along the type ring) and world up as the ring plane axes,
            // so the ring plane normal points radially outward.
            const up = new THREE.Vector3(0, 1, 0);
            const radial = center.clone();
            const upComp = up.clone().multiplyScalar(radial.dot(up));
            radial.sub(upComp);
            if (radial.lengthSq() < 1e-6) radial.set(1, 0, 0);
            radial.normalize();
            // tangent = up x radial (lies in XZ plane, direction of type ring curvature)
            const tangent = new THREE.Vector3().crossVectors(up, radial);
            if (tangent.lengthSq() < 1e-6) tangent.set(0, 0, 1);
            tangent.normalize();
            const uAxis = tangent;
            const vAxis = up;

            const inputs = entries.filter(e => e.kind === 'input').sort((a, b) => a.name.localeCompare(b.name));
            const outputs = entries.filter(e => e.kind === 'output').sort((a, b) => a.name.localeCompare(b.name));
            const ordered = [...inputs, ...outputs];
            const ringEntities = ordered.map(r => ({ id: r.id, name: r.name, description: r.description }));

            const delta = drawRingBasis(
                this.root,
                groupName,
                ringEntities,
                center,
                ringCfg as typeof baseRolesCfg,
                { u: uAxis, v: vAxis },
                {
                    orientationMode: 'given',
                    semicircleBy: (entity) => inputs.some(i => i.id === entity.id) ? 'upper' : 'lower',
                    nameBy: (entity) => String((entity as unknown as { name?: unknown }).name ?? entity.id),
                }
            );

            // If drawing for animation, make role meshes invisible initially
            if (forAnimationJobId && delta[groupName]) {
                for (const rec of delta[groupName]) {
                    for (const info of Object.values(rec)) {
                        const mesh = (info as { mesh?: THREE.Mesh }).mesh;
                        if (mesh) mesh.visible = false;
                    }
                }
            }

            for (const [key, arr] of Object.entries(delta)) {
                if (!this.entityMeshMap[key]) this.entityMeshMap[key] = [];
                this.entityMeshMap[key].push(...arr);
            }
            // Draw link lines if we have job position and freshly created role meshes
            if (jobPos && delta[groupName]) {
                const roleMeshById = new Map<string, THREE.Mesh>();
                for (const rec of delta[groupName]) {
                    for (const [rid, info] of Object.entries(rec)) {
                        const mesh = (info as { mesh?: THREE.Mesh }).mesh;
                        if (mesh) {
                            roleMeshById.set(rid, mesh);
                        }
                    }
                }
                const inputs = entries.filter(e => e.kind === 'input');
                const outputs = entries.filter(e => e.kind === 'output');

                for (const r of inputs) {
                    const rm = roleMeshById.get(r.id);
                    if (rm) {
                        const line = makeLine(jobPos, rm.position, 0x00cc66);
                        // If drawing for animation, make line invisible initially
                        if (forAnimationJobId) line.visible = false;
                        linkGroup.add(line);
                        roleConnectors.push(line);
                    }
                }
                for (const r of outputs) {
                    const rm = roleMeshById.get(r.id);
                    if (rm) {
                        const line = makeLine(jobPos, rm.position, 0xcc0033);
                        // If drawing for animation, make line invisible initially
                        if (forAnimationJobId) line.visible = false;
                        linkGroup.add(line);
                        roleConnectors.push(line);
                    }
                }
                // console.log('drawRoles: added', roleConnectors.length, 'connectors to linkGroup for job', jobId, 'initial visible =', !forAnimationJobId);
            }
        }

        // After processing all types, register all accumulated connectors for animation
        if (forAnimationJobId && jobMesh && roleConnectors.length > 0) {
            // console.log('drawRoles: registering role connectors for job', jobId, 'connectors count =', roleConnectors.length);
            this.jobAnimation?.setTypeConnectors(jobMesh, roleConnectors);
        } else if (forAnimationJobId) {
            // console.log('drawRoles: no connectors registered for job', jobId, 'jobMesh exists =', !!jobMesh, 'roleConnectors.length =', roleConnectors.length);
        }
    }

    private drawPrimitiveResources() {
        // console.log('=== Starting drawPrimitiveResources ===');
        const n = getNames();
        const s = getSpecials();
        const resourcesByType = this.resourceDataMap;
        if (!resourcesByType) {
            // console.log('No resourcesByType, returning early');
            return;
        }

        // console.log('Computing framework base radius...');
        const sharedBase = computeFrameworkBaseRadius(this.explorerConfig.world);
        const sphereRadius = sharedBase + (this.explorerConfig.world.sphere?.radiusOffset ?? 16);

        // console.log('Creating resources group...');
        const group = new THREE.Group();
        const groupName = n.resources;
        group.name = groupName;
        this.root.add(group);

        // console.log('Setting up line materials...');

        const typeConnectorCfg = this.explorerConfig.world.lines.typeConnector;
        const typeConnectorMat = new THREE.LineBasicMaterial({ color: typeConnectorCfg.material.color, transparent: typeConnectorCfg.material.opacity < 1, opacity: typeConnectorCfg.material.opacity, depthWrite: typeConnectorCfg.material.depthWrite, depthTest: typeConnectorCfg.material.depthTest });
        const edgeMat = new THREE.LineBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.5, depthWrite: false, depthTest: false });

        // ATTENTION: consider extracting known specials directly
        const parentEntries = this.meshEntriesFromEntityGroup(n.types);

        const TYPE_Boolean_ID = s.TYPE_Boolean;
        const booleanItems = resourcesByType[TYPE_Boolean_ID] ?? [];
        if (booleanItems.length > 0) {
            // Do not mutate the shared resourceDataMap; just render caps from available items
            const typeMesh = this.meshEntriesFromEntityGroup(n.types).find(t => t.id === TYPE_Boolean_ID)?.mesh;
            const typePos = typeMesh?.position.clone() ?? new THREE.Vector3(0, 0, 0);
            const makePolarCap = (isNorth: boolean, item: ResourceDataJson) => {
                const delta = this.explorerConfig.world.poleCaps?.delta ?? 0.12;
                const radiusScale = this.explorerConfig.world.poleCaps?.radiusScale ?? 1.0;
                const color = isNorth ? (this.explorerConfig.world.poleCaps?.colorTrue ?? 0x66ff99) : (this.explorerConfig.world.poleCaps?.colorFalse ?? 0xff6699);
                const opacity = this.explorerConfig.world.poleCaps?.opacity ?? 0.85;
                const lat = isNorth ? (Math.PI / 2 - delta) : (-Math.PI / 2 + delta);
                const y = sphereRadius * Math.sin(lat);
                const r = sphereRadius * Math.cos(lat) * radiusScale;

                const discGeometry = new THREE.CircleGeometry(r, 64);
                const discMaterial = new THREE.MeshBasicMaterial({ color, transparent: opacity < 1, opacity, depthWrite: false, depthTest: false, side: THREE.DoubleSide });
                const discMesh = new THREE.Mesh(discGeometry, discMaterial);
                discMesh.rotation.x = isNorth ? -Math.PI / 2 : Math.PI / 2;
                const azimuth = Math.atan2(typePos.z, typePos.x);
                discMesh.rotation.y = azimuth;
                discMesh.position.set(0, y, 0);
                const id = String(item.id ?? '');
                const extractedData = item.extractedData as { identity?: string } | null;
                const name = String(extractedData?.identity ?? '');
                discMesh.userData = { entity: groupName, id, name, booleanCap: true, isNorth };
                discMesh.renderOrder = 10001;
                group.add(discMesh);

                const centerPos = new THREE.Vector3(0, y, 0);
                const line = new THREE.Line(new THREE.BufferGeometry().setFromPoints([typePos.clone(), centerPos]), typeConnectorMat.clone());
                line.renderOrder = 998;
                group.add(line);
            };
            const TRUE_ID = s.BOOLEAN_true;
            const FALSE_ID = s.BOOLEAN_false;
            const trueItem = booleanItems.find(r => r.id === TRUE_ID);
            const falseItem = booleanItems.find(r => r.id === FALSE_ID);
            if (!trueItem) {
                throw new Error('Missing required BOOLEAN-true resource data item');
            }
            if (!falseItem) {
                throw new Error('Missing required BOOLEAN-false resource data item');
            }
            makePolarCap(false, trueItem);
            makePolarCap(true, falseItem);
        }

        // Grid-aligned layout: map each non-Boolean resource to a full grid cell along a single longitude just outside parent type.
        const { latPanels: gridLatPanels, lonPanels: gridLonPanels, equatorLatIdx } = resolveGridSegments(this.explorerConfig.world, sphereRadius);
        const panelLatSize = Math.PI / gridLatPanels;
        const panelLonSize = (2 * Math.PI) / gridLonPanels;
        const insetFrac = 0; // no inset so panels align perfectly with grid

        const resMatConfig = this.explorerConfig.world.panels.resources.material;
        const resMat = this.makeMaterialFromConfig(resMatConfig);

        // Helper: numeric sort for Integer resources (by identity) ascending
        const TYPE_Integer_ID = s.TYPE_Integer;
        const sortIntegerResources = (items: ResourceDataJson[]): ResourceDataJson[] => {
            return [...items].sort((a, b) => {
                const av = typeof (a as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity === 'number'
                    ? Number((a as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity) : 0;
                const bv = typeof (b as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity === 'number'
                    ? Number((b as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity) : 0;
                return av - bv;
            });
        };

        for (const parent of parentEntries) {
            if (parent.id !== TYPE_Integer_ID) continue; // ATTENTION: handling only Intgers for now
            const rawItems = (resourcesByType[parent.id] ?? []) as ResourceDataJson[];
            if (!rawItems.length) continue;
            const parentPos = parent.mesh.position.clone();
            const parentLon = Math.atan2(parentPos.z, parentPos.x);
            let lonIdx = Math.round((parentLon + Math.PI) / panelLonSize);
            lonIdx = ((lonIdx % gridLonPanels) + gridLonPanels) % gridLonPanels;
            const ident = (res: ResourceDataJson) => {
                const r = res as unknown as { path?: unknown; extractedData?: { identity?: unknown } };
                return { id: String(r.path ?? ''), name: String(r.extractedData?.identity ?? '') };
            };
            // Draw just ONE link per type: from the type's position to the equator point at its assigned longitude.
            const equatorLat = 0; // latitude 0 (equator)
            const lonMid = -Math.PI + (lonIdx + 0.5) * panelLonSize; // center of chosen longitude panel
            const equatorPoint = new THREE.Vector3(
                sphereRadius * Math.cos(equatorLat) * Math.cos(lonMid),
                sphereRadius * Math.sin(equatorLat),
                sphereRadius * Math.cos(equatorLat) * Math.sin(lonMid)
            );
            const typeEquatorLink = new THREE.Line(
                new THREE.BufferGeometry().setFromPoints([parentPos.clone(), equatorPoint]),
                typeConnectorMat.clone()
            );
            typeEquatorLink.renderOrder = 998;
            group.add(typeEquatorLink);

            let items = rawItems;
            const isIntegerType = parent.id === TYPE_Integer_ID;
            if (isIntegerType) {
                items = sortIntegerResources(rawItems);
            }

            // For Integer resources: place 0 at equator row, negatives below, positives above.
            // For others: keep previous behavior (northward from equator).
            let zeroItem: ResourceDataJson | undefined;
            const negativeItems: ResourceDataJson[] = [];
            const positiveItems: ResourceDataJson[] = [];
            if (isIntegerType) {
                for (const it of items) {
                    const val = (it as unknown as { extractedData?: { identity?: unknown } }).extractedData?.identity;
                    if (typeof val === 'number') {
                        if (val === 0 && !zeroItem) zeroItem = it; // first zero only
                        else if (val < 0) negativeItems.push(it);
                        else if (val > 0) positiveItems.push(it);
                        else if (val === 0) positiveItems.push(it); // additional zeros treated as positive for layout simplicity
                    }
                }
                // Arrange negatives descending away from equator: closest (largest negative or smallest absolute?) We'll keep ascending (more negative further).
                const getIntVal = (r: ResourceDataJson) => {
                    const exposed: unknown = (r as unknown as { extractedData?: unknown }).extractedData;
                    const sem: unknown = (exposed as { identity?: unknown })?.identity;
                    return typeof sem === 'number' ? sem : 0;
                };
                // Sort negatives so values closer to zero (e.g. -1) are placed nearest the equator,
                // and more negative values (e.g. -5) are pushed further away (southward).
                negativeItems.sort((a, b) => getIntVal(b) - getIntVal(a)); // descending (e.g., -1,-5)
                positiveItems.sort((a, b) => getIntVal(a) - getIntVal(b)); // ascending (1,2,3)
            }

            const buildPanelAtLatIdx = (resource: ResourceDataJson, latIdx: number) => {
                if (latIdx <= 0 || latIdx >= gridLatPanels - 1) return; // avoid poles
                const lat0 = -Math.PI / 2 + latIdx * panelLatSize + insetFrac * panelLatSize;
                const lat1 = -Math.PI / 2 + (latIdx + 1) * panelLatSize - insetFrac * panelLatSize;
                const lon0 = -Math.PI + lonIdx * panelLonSize + insetFrac * panelLonSize;
                const lon1 = -Math.PI + (lonIdx + 1) * panelLonSize - insetFrac * panelLonSize;
                const segLat = 6;
                const segLon = 6;
                const positions: number[] = [];
                const uvs: number[] = [];
                const sphericalToCartesian = (r: number, lat: number, lon: number) =>
                    new THREE.Vector3(
                        r * Math.cos(lat) * Math.cos(lon),
                        r * Math.sin(lat),
                        r * Math.cos(lat) * Math.sin(lon)
                    );
                for (let iy = 0; iy <= segLat; iy++) {
                    const t = iy / segLat;
                    const lat = lat0 + (lat1 - lat0) * t;
                    for (let ix = 0; ix <= segLon; ix++) {
                        const s = ix / segLon;
                        const lon = lon0 + (lon1 - lon0) * s;
                        const p = sphericalToCartesian(sphereRadius, lat, lon);
                        positions.push(p.x, p.y, p.z);
                        uvs.push((lon + Math.PI) / (2 * Math.PI), (lat + Math.PI / 2) / Math.PI);
                    }
                }
                const indices: number[] = [];
                const cols = segLon + 1;
                for (let iy = 0; iy < segLat; iy++) {
                    for (let ix = 0; ix < segLon; ix++) {
                        const a = iy * cols + ix;
                        const b = a + 1;
                        const c = (iy + 1) * cols + ix + 1;
                        const d = (iy + 1) * cols + ix;
                        indices.push(a, d, b, b, d, c);
                    }
                }
                const geom = new THREE.BufferGeometry();
                geom.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
                geom.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
                geom.setIndex(indices);
                geom.computeVertexNormals();
                const panel = new THREE.Mesh(geom, resMat.clone());
                const rid = ident(resource);
                panel.userData = { entity: groupName, id: rid.id, name: rid.name };
                panel.renderOrder = 9998;
                const edgeSamples = 24;
                const buildEdge = (points: THREE.Vector3[]) => {
                    const eg = new THREE.BufferGeometry().setFromPoints(points);
                    const el = new THREE.Line(eg, edgeMat.clone());
                    el.renderOrder = 10000;
                    return el;
                };
                const topPts: THREE.Vector3[] = [];
                for (let k = 0; k <= edgeSamples; k++) {
                    const lon = lon0 + (lon1 - lon0) * (k / edgeSamples);
                    topPts.push(sphericalToCartesian(sphereRadius, lat0, lon));
                }
                const bottomPts: THREE.Vector3[] = [];
                for (let k = 0; k <= edgeSamples; k++) {
                    const lon = lon0 + (lon1 - lon0) * (k / edgeSamples);
                    bottomPts.push(sphericalToCartesian(sphereRadius, lat1, lon));
                }
                const leftPts: THREE.Vector3[] = [];
                for (let k = 0; k <= edgeSamples; k++) {
                    const lat = lat0 + (lat1 - lat0) * (k / edgeSamples);
                    leftPts.push(sphericalToCartesian(sphereRadius, lat, lon0));
                }
                const rightPts: THREE.Vector3[] = [];
                for (let k = 0; k <= edgeSamples; k++) {
                    const lat = lat0 + (lat1 - lat0) * (k / edgeSamples);
                    rightPts.push(sphericalToCartesian(sphereRadius, lat, lon1));
                }
                panel.add(buildEdge(topPts));
                panel.add(buildEdge(bottomPts));
                panel.add(buildEdge(leftPts));
                panel.add(buildEdge(rightPts));
                group.add(panel);
            };

            if (isIntegerType) {
                // Place the zero panel first at equator row
                if (zeroItem) buildPanelAtLatIdx(zeroItem, equatorLatIdx);
                // Negative integers: below equator, moving southward (equatorLatIdx-1, equatorLatIdx-2, ...)
                for (let i = 0; i < negativeItems.length; i++) {
                    const latIdx = equatorLatIdx - 1 - i;
                    if (latIdx <= 0) break;
                    buildPanelAtLatIdx(negativeItems[i], latIdx);
                }
                // Positive integers: above equator, moving northward (equatorLatIdx+1, equatorLatIdx+2, ...)
                for (let i = 0; i < positiveItems.length; i++) {
                    const latIdx = equatorLatIdx + 1 + i;
                    if (latIdx >= gridLatPanels - 1) break;
                    buildPanelAtLatIdx(positiveItems[i], latIdx);
                }
            } else {
                // Original behavior for non-integer types (progress northward only)
                for (let i = 0; i < items.length; i++) {
                    const latIdx = equatorLatIdx + 1 + i;
                    if (latIdx >= gridLatPanels - 1) break;
                    buildPanelAtLatIdx(items[i], latIdx);
                }
            }
        }

    }

    // Implement optional WorldInterface method: find mesh by entity name and id
    // Resolves entity name from config if it matches a known entity type (e.g., CONSTANTS.ARCHETYPES.types -> config names.types)
    public getMeshById(entity: string, id: string): THREE.Mesh | undefined {
        const n = getNames();
        // Map common entity constants to config-resolved names
        let resolvedEntity = entity;
        if (entity === CONSTANTS.ARCHETYPES.types) resolvedEntity = n.types;
        else if (entity === CONSTANTS.ARCHETYPES.formats) resolvedEntity = n.formats;
        else if (entity === CONSTANTS.RESOURCES.resources) resolvedEntity = n.resources;
        else if (entity === CONSTANTS.RESOURCES.jobs) resolvedEntity = n.jobs;
        else if (entity === CONSTANTS.ARCHETYPES_PSEUDO.roles) resolvedEntity = n.roles;
        return findMeshById(this.scene, resolvedEntity, id);
    }

    // Animation control methods

    /**
     * Start the job processing animation
     */
    public startJobAnimation(): void {
        this.jobAnimation?.start();
    }

    /**
     * Stop the job processing animation and reset jobs to original positions
     */
    public stopJobAnimation(): void {
        this.jobAnimation?.stop();
    }

    /**
     * Pause the job processing animation
     */
    public pauseJobAnimation(): void {
        this.jobAnimation?.pause();
    }

    /**
     * Resume the job processing animation
     */
    public resumeJobAnimation(): void {
        this.jobAnimation?.resume();
    }

    /**
     * Check if job animation is currently running
     */
    public isJobAnimationActive(): boolean {
        return this.jobAnimation?.isActive() ?? false;
    }

    /**
     * Cleanup when world is destroyed
     */
    public override dispose(): void {
        this.jobAnimation?.stop();
        super.dispose();
    }

}
