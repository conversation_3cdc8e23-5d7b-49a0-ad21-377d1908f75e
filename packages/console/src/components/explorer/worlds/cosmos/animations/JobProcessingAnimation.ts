import * as THREE from 'three';

export interface JobAnimationConfig {
    /** Duration of pull-in phase in milliseconds */
    pullInDuration: number;
    /** Duration of pull-out phase in milliseconds */
    pullOutDuration: number;
    /** Pause between jobs in milliseconds */
    pauseBetweenJobs: number;
    /** Pause while job is inside the engine in milliseconds */
    pauseInside: number;
    /** How far into the engine the job should go (0 = surface, 1 = center) */
    pullInDepth: number;
    /** Easing function for pull-in */
    pullInEasing: (t: number) => number;
    /** Easing function for pull-out */
    pullOutEasing: (t: number) => number;
    /** Color to highlight active job (optional) */
    highlightJobColor?: THREE.ColorRepresentation;
    /** Color to highlight engine while processing (optional) */
    highlightEngineColor?: THREE.ColorRepresentation;
    /** Callback when a job starts being animated - use to draw roles */
    onJobStartAnimation?: (jobId: string) => void;
    /** Callback when a job finishes being animated - use to clear roles */
    onJobEndAnimation?: (jobId: string) => void;
    /** Callback to show/hide role meshes and connectors */
    onToggleRoleVisibility?: (jobId: string, visible: boolean) => void;
    /** Callback when animation stops - use to re-enable interactions */
    onAnimationStop?: () => void;
}

export interface JobAnimationTarget {
    mesh: THREE.Mesh;
    originalPosition: THREE.Vector3;
    originalColor: THREE.Color;
    spoke?: THREE.Line;
    typeConnectors?: THREE.Line[]; // Lines connecting job to its input/output roles
}

enum AnimationPhase {
    PAUSING = 'pausing',
    PULLING_IN = 'pulling_in',
    INSIDE = 'inside',
    PULLING_OUT = 'pulling_out',
}

/**
 * Manages the animation of jobs being processed by the Engine.
 * Jobs are pulled in one at a time, then returned to their original positions.
 */
export class JobProcessingAnimation {
    private jobs: JobAnimationTarget[] = [];
    private currentJobIndex: number = 0;
    private phase: AnimationPhase = AnimationPhase.PAUSING;
    private phaseStartTime: number = 0;
    private isRunning: boolean = false;
    private engineMesh: THREE.Mesh | null = null;
    private engineOriginalColor: THREE.Color | null = null;

    constructor(
        private enginePosition: THREE.Vector3,
        private config: JobAnimationConfig
    ) { }

    /**
     * Add a job to the animation sequence
     */
    public addJob(mesh: THREE.Mesh, spoke?: THREE.Line): void {
        // Store original emissive color
        const material = mesh.material as THREE.MeshStandardMaterial;
        const originalColor = material.emissive.clone();

        this.jobs.push({
            mesh,
            originalPosition: mesh.position.clone(),
            originalColor,
            spoke,
            typeConnectors: [], // Will be populated by setTypeConnectors
        });
        console.log('[JobProcessingAnimation.addJob] Added job, total jobs:', this.jobs.length, 'jobId:', mesh.userData?.id);
    }

    /**
     * Set the engine mesh for highlighting
     */
    setEngineMesh(mesh: THREE.Mesh): void {
        this.engineMesh = mesh;
        const material = mesh.material as THREE.MeshStandardMaterial;
        this.engineOriginalColor = material.emissive.clone();
    }

    /**
     * Set role connector lines for a specific job
     * These lines connect the job to its input/output roles
     */
    public setTypeConnectors(jobMesh: THREE.Mesh, connectors: THREE.Line[]): void {
        const jobId = jobMesh.userData?.id;
        console.log('JobProcessingAnimation.setTypeConnectors: called for jobId', jobId, 'with', connectors.length, 'connectors');
        const job = this.jobs.find(j => j.mesh === jobMesh);
        if (job) {
            console.log('JobProcessingAnimation.setTypeConnectors: found job, setting connectors');
            job.typeConnectors = connectors;
        } else {
            console.log('JobProcessingAnimation.setTypeConnectors: job NOT found for mesh');
        }
    }

    /**
     * Clear all jobs from the animation
     */
    public clearJobs(): void {
        // Reset any active highlights
        this.resetHighlights();

        // Clear animation-controlled flags from all jobs
        for (const job of this.jobs) {
            if (job.mesh.userData.__animationControlled) {
                delete job.mesh.userData.__animationControlled;
            }
        }

        this.jobs = [];
        this.currentJobIndex = 0;
        this.phase = AnimationPhase.PAUSING;
    }

    /**
     * Start the animation loop
     */
    public start(): void {
        console.log('[JobProcessingAnimation.start] Called, current state:', {
            isRunning: this.isRunning,
            jobCount: this.jobs.length,
            hasEngineMesh: !!this.engineMesh
        });
        if (this.isRunning) {
            console.log('[JobProcessingAnimation.start] Already running, ignoring');
            return;
        }
        if (this.jobs.length === 0) {
            console.log('[JobProcessingAnimation.start] No jobs to animate, ignoring');
            return;
        }

        console.log('[JobProcessingAnimation.start] Starting animation with', this.jobs.length, 'jobs');
        this.isRunning = true;
        this.phaseStartTime = performance.now();
    }

    /**
     * Stop the animation and reset all jobs to original positions
     */
    public stop(): void {
        this.isRunning = false;

        // Reset highlights
        this.resetHighlights();

        // Reset all jobs to original positions and clear animation-controlled flags
        for (const job of this.jobs) {
            job.mesh.position.copy(job.originalPosition);
            if (job.spoke) {
                this.updateSpoke(job.spoke, this.enginePosition, job.originalPosition);
            }
            // Clear animation-controlled flag to allow hover interaction when stopped
            if (job.mesh.userData.__animationControlled) {
                delete job.mesh.userData.__animationControlled;
            }
        }

        // Re-enable all entity interactions via callback
        if (this.config.onAnimationStop) {
            this.config.onAnimationStop();
        }
    }

    /**
     * Pause the animation
     */
    public pause(): void {
        this.isRunning = false;
    }

    /**
     * Resume the animation
     */
    public resume(): void {
        if (this.isRunning) return;
        if (this.jobs.length === 0) return;

        this.isRunning = true;
        this.phaseStartTime = performance.now();
    }

    /**
     * Check if animation is currently running
     */
    public isActive(): boolean {
        return this.isRunning;
    }

    /**
     * Get the ID of the currently animating job (if any)
     */
    public getCurrentJobId(): string | null {
        if (!this.isRunning || this.jobs.length === 0) return null;
        const currentJob = this.jobs[this.currentJobIndex];
        if (!currentJob) return null;
        return currentJob.mesh.userData?.id ?? null;
    }

    /**
     * Update animation state (called from main render loop)
     */
    public update(): void {
        if (!this.isRunning) return;

        const now = performance.now();
        const elapsed = now - this.phaseStartTime;

        const currentJob = this.jobs[this.currentJobIndex];
        if (!currentJob) {
            this.isRunning = false;
            return;
        }

        switch (this.phase) {
            case AnimationPhase.PAUSING:
                if (elapsed >= this.config.pauseBetweenJobs) {
                    this.phase = AnimationPhase.PULLING_IN;
                    this.phaseStartTime = now;
                    // Highlight the job being pulled in
                    this.highlightJob(currentJob);
                }
                break;

            case AnimationPhase.PULLING_IN:
                if (elapsed >= this.config.pullInDuration) {
                    // Pull-in complete, pause inside
                    this.phase = AnimationPhase.INSIDE;
                    this.phaseStartTime = now;
                    // Reset job highlight color only (keep connectors visible) and highlight engine
                    this.resetJobHighlightColor(currentJob);
                    this.highlightEngine();
                } else {
                    // Animate pull-in
                    const t = elapsed / this.config.pullInDuration;
                    const easedT = this.config.pullInEasing(t);
                    this.updateJobPosition(currentJob, easedT);
                }
                break;

            case AnimationPhase.INSIDE:
                if (elapsed >= this.config.pauseInside) {
                    // Inside pause complete, start pull-out
                    this.phase = AnimationPhase.PULLING_OUT;
                    this.phaseStartTime = now;
                    // Reset engine highlight and re-highlight job for pull-out
                    this.resetEngineHighlight();
                    this.highlightJob(currentJob);
                }
                // Job stays at full pull-in depth during this phase
                break;

            case AnimationPhase.PULLING_OUT:
                if (elapsed >= this.config.pullOutDuration) {
                    // Pull-out complete, reset job and move to next
                    currentJob.mesh.position.copy(currentJob.originalPosition);
                    if (currentJob.spoke) {
                        this.updateSpoke(currentJob.spoke, this.enginePosition, currentJob.originalPosition);
                    }

                    // Reset job highlights and hide connectors/roles
                    this.resetJobHighlight(currentJob);

                    // Move to next job
                    this.currentJobIndex = (this.currentJobIndex + 1) % this.jobs.length;
                    this.phase = AnimationPhase.PAUSING;
                    this.phaseStartTime = now;
                } else {
                    // Animate pull-out
                    const t = elapsed / this.config.pullOutDuration;
                    const easedT = this.config.pullOutEasing(t);
                    this.updateJobPosition(currentJob, 1 - easedT);
                }
                break;
        }
    }

    /**
     * Highlight the active job
     */
    private highlightJob(job: JobAnimationTarget): void {
        if (!this.config.highlightJobColor) {
            console.log('JobProcessingAnimation: No highlightJobColor configured');
            return;
        }

        const material = job.mesh.material as THREE.MeshStandardMaterial;

        // Mark mesh as animation-controlled to prevent XrInteractor from overriding
        job.mesh.userData.__animationControlled = true;

        material.emissive.set(this.config.highlightJobColor);

        // Trigger callback to draw roles (creates connectors & meshes)
        const jobId = job.mesh.userData.id;
        if (jobId && this.config.onJobStartAnimation) {
            this.config.onJobStartAnimation(jobId);
        }

        // Now that drawRoles has registered connectors, show them
        if (job.typeConnectors) {
            job.typeConnectors.forEach(connector => {
                connector.visible = true;
            });
        }

        // Show role meshes
        if (jobId && this.config.onToggleRoleVisibility) {
            this.config.onToggleRoleVisibility(jobId, true);
        }
    }

    /**
     * Highlight the engine
     */
    private highlightEngine(): void {
        if (!this.config.highlightEngineColor) {
            return;
        }
        if (!this.engineMesh) {
            return;
        }

        const material = this.engineMesh.material as THREE.MeshStandardMaterial;

        // Mark mesh as animation-controlled to prevent XrInteractor from overriding
        this.engineMesh.userData.__animationControlled = true;

        material.emissive.set(this.config.highlightEngineColor);
    }

    /**
     * Reset job highlight color only (keep connectors/roles visible)
     */
    private resetJobHighlightColor(job: JobAnimationTarget): void {
        const material = job.mesh.material as THREE.MeshStandardMaterial;
        material.emissive.copy(job.originalColor);
        // Allow XrInteractor to control this mesh again
        delete job.mesh.userData.__animationControlled;
    }

    /**
     * Reset job highlight and hide its connectors/roles
     */
    private resetJobHighlight(job: JobAnimationTarget): void {
        this.resetJobHighlightColor(job);

        // Hide type connectors (role lines) for this job
        if (job.typeConnectors) {
            job.typeConnectors.forEach(connector => connector.visible = false);
        }

        // Trigger callback to clear roles
        const jobId = job.mesh.userData.id;
        if (jobId && this.config.onJobEndAnimation) {
            this.config.onJobEndAnimation(jobId);
        }

        // Hide role meshes
        if (jobId && this.config.onToggleRoleVisibility) {
            this.config.onToggleRoleVisibility(jobId, false);
        }
    }

    /**
     * Reset engine highlight to original color
     */
    private resetEngineHighlight(): void {
        if (this.engineMesh && this.engineOriginalColor) {
            const material = this.engineMesh.material as THREE.MeshStandardMaterial;
            material.emissive.copy(this.engineOriginalColor);
            // Allow XrInteractor to control this mesh again
            delete this.engineMesh.userData.__animationControlled;
        }
    }

    /**
     * Reset all highlights to original colors (convenience method)
     */
    private resetHighlights(): void {
        const currentJob = this.jobs[this.currentJobIndex];
        if (currentJob) {
            this.resetJobHighlight(currentJob);
        }
        this.resetEngineHighlight();
    }

    /**
     * Update job position based on animation progress
     * @param job The job to update
     * @param t Progress from 0 (original position) to 1 (inside engine)
     */
    private updateJobPosition(job: JobAnimationTarget, t: number): void {
        // Calculate target position (interpolate between original position and engine position)
        const targetPos = new THREE.Vector3().lerpVectors(
            job.originalPosition,
            this.enginePosition,
            t * this.config.pullInDepth
        );

        job.mesh.position.copy(targetPos);

        // Update spoke if it exists
        if (job.spoke) {
            this.updateSpoke(job.spoke, this.enginePosition, targetPos);
        }

        // Update role connector lines if they exist
        if (job.typeConnectors) {
            for (const connector of job.typeConnectors) {
                const positions = connector.geometry.attributes.position.array as Float32Array;
                // Update first endpoint (job position) while keeping second endpoint (role position) unchanged
                positions[0] = targetPos.x;
                positions[1] = targetPos.y;
                positions[2] = targetPos.z;
                connector.geometry.attributes.position.needsUpdate = true;
            }
        }
    }

    /**
     * Update spoke geometry to connect engine to job
     */
    private updateSpoke(spoke: THREE.Line, enginePos: THREE.Vector3, jobPos: THREE.Vector3): void {
        const positions = new Float32Array([
            enginePos.x, enginePos.y, enginePos.z,
            jobPos.x, jobPos.y, jobPos.z,
        ]);
        spoke.geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        spoke.geometry.attributes.position.needsUpdate = true;
    }
}

/**
 * Common easing functions
 */
export const EasingFunctions = {
    /** Linear easing (no acceleration) */
    linear: (t: number): number => t,

    /** Ease in (accelerate from zero velocity) */
    easeInQuad: (t: number): number => t * t,

    /** Ease out (decelerate to zero velocity) */
    easeOutQuad: (t: number): number => t * (2 - t),

    /** Ease in-out (accelerate then decelerate) */
    easeInOutQuad: (t: number): number => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),

    /** Ease in cubic (stronger acceleration) */
    easeInCubic: (t: number): number => t * t * t,

    /** Ease out cubic (stronger deceleration) */
    easeOutCubic: (t: number): number => (--t) * t * t + 1,

    /** Ease in-out cubic */
    easeInOutCubic: (t: number): number => (t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1),

    /** Elastic ease in */
    easeInElastic: (t: number): number => {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
    },

    /** Elastic ease out */
    easeOutElastic: (t: number): number => {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
    },
};

/**
 * Default animation configuration
 */
export const defaultJobAnimationConfig: JobAnimationConfig = {
    pullInDuration: 800,        // 0.8 seconds to pull in
    pullOutDuration: 600,       // 0.6 seconds to pull out
    pauseBetweenJobs: 200,      // 0.2 seconds pause between jobs
    pauseInside: 400,           // 0.4 seconds pause while inside
    pullInDepth: 0.95,          // Pull job 95% of the way to engine center
    pullInEasing: EasingFunctions.easeInOutCubic,
    pullOutEasing: EasingFunctions.easeOutCubic,
};
