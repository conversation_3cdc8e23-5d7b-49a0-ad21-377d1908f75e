# Job Processing Animation

This animation system creates a visual representation of the Engine processing jobs by pulling them in one at a time.

## Files

- **`JobProcessingAnimation.ts`**: Core animation logic and configuration
- **`CosmosWorld.ts`**: Integration into the Cosmos world

## How It Works

1. **Setup**: When jobs are drawn in `drawJobs()`, each job mesh and its spoke are registered with the animation system
2. **Animation Loop**: The animation cycles through jobs sequentially:
   - **Pause**: Brief pause before pulling the next job
   - **Pull In**: Job moves toward the Engine (spoke shortens)
   - **Pull Out**: Job returns to original position (spoke lengthens)
   - **Repeat**: Move to next job and continue the loop

3. **Visual Effect**: 
   - Jobs smoothly animate from their ring position toward the Engine
   - The connecting spoke updates in real-time to maintain the connection
   - Uses easing functions for smooth, natural motion

## Configuration

Default settings in `defaultJobAnimationConfig`:
```typescript
{
    pullInDuration: 800,        // 0.8 seconds to pull in
    pullOutDuration: 600,       // 0.6 seconds to pull out
    pauseBetweenJobs: 200,      // 0.2 seconds pause between jobs
    pullInDepth: 0.95,          // Pull job 95% toward engine center
    pullInEasing: easeInOutCubic,
    pullOutEasing: easeOutCubic,
}
```

## API

The `CosmosWorld` class provides these public methods:

- `startJobAnimation()`: Start the animation
- `stopJobAnimation()`: Stop and reset all jobs to original positions
- `pauseJobAnimation()`: Pause without resetting
- `resumeJobAnimation()`: Resume from paused state
- `isJobAnimationActive()`: Check if animation is running

## Easing Functions

Available in `EasingFunctions`:
- `linear`: No acceleration/deceleration
- `easeInQuad`, `easeOutQuad`, `easeInOutQuad`: Quadratic easing
- `easeInCubic`, `easeOutCubic`, `easeInOutCubic`: Cubic easing
- `easeInElastic`, `easeOutElastic`: Elastic bounce effect

## Customization

To customize the animation, modify the config when creating the animation:

```typescript
// In CosmosWorld constructor:
this.jobAnimation = new JobProcessingAnimation(
    new THREE.Vector3(0, 0, 0),
    {
        pullInDuration: 1000,
        pullOutDuration: 800,
        pauseBetweenJobs: 300,
        pullInDepth: 0.9,
        pullInEasing: EasingFunctions.easeInOutCubic,
        pullOutEasing: EasingFunctions.easeOutElastic,
    }
);
```

## Auto-Start

The animation automatically starts when:
1. Jobs are drawn/redrawn in the scene
2. There is at least one job to animate

The animation automatically stops when:
1. The scene is redrawn (to prevent conflicts)
2. `dispose()` is called on the world
3. Manually via `stopJobAnimation()`
