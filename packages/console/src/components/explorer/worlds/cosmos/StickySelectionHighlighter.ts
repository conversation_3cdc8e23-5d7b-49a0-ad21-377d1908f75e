import * as THREE from 'three';
import type { WorldInterface } from '@/explorer/worlds/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';

export class StickySelectionHighlighter {
    private scene: THREE.Scene;
    private world: WorldInterface;
    private highlighted: THREE.Object3D | null = null;
    private intRingsGroup: THREE.Group | null = null;
    private connectionsGroup: THREE.Group | null = null;
    private roleLinesGroup: THREE.Group | null = null;
    private awaitingIntegerResources = false;
    private selectedResourceIds: Set<string> = new Set();

    // Colors
    private stickyEmissive = 0xffcc33; // brighter, high-visibility amber
    private guideColor = 0x00ff00; // green
    private inputLineColor = 0x00cc66; // greenish
    private outputLineColor = 0xcc0033; // red

    constructor(scene: THREE.Scene, world: WorldInterface) {
        this.scene = scene;
        this.world = world;
    }

    // --- Helpers: discover type meshes from the scene graph (config/data agnostic) ---
    private getTypeMeshes(): Array<{ id: string; name: string; mesh: THREE.Mesh }> {
        const results: Array<{ id: string; name: string; mesh: THREE.Mesh }> = [];
        this.scene.traverse((child: THREE.Object3D) => {
            const ud = child.userData as { entity?: unknown; id?: unknown; name?: unknown } | undefined;
            if (ud && ud.entity === CONSTANTS.ARCHETYPES.types && (child as unknown as { isMesh?: boolean }).isMesh) {
                const mesh = child as THREE.Mesh;
                const id = String(ud.id ?? '');
                const name = String(ud.name ?? id);
                results.push({ id, name, mesh });
            }
        });
        return results;
    }

    private findTypeById(typeId: string): { id: string; name: string; mesh: THREE.Mesh } | undefined {
        if (!typeId) return undefined;
        const all = this.getTypeMeshes();
        return all.find(t => t.id === typeId);
    }

    private isIntegerTypeName(name: string): boolean {
        return /int|integer/i.test(name ?? '');
    }

    // Call this every frame after interactor.updateInteraction()
    tick() {
        if (!this.highlighted) return;
        this.applyHighlight(this.highlighted!, true);
    }

    // Call this when selection changes (e.g., each frame with current selectedObject)
    onSelection(selected: THREE.Object3D | null) {
        const ent = selected?.userData?.entity as string | undefined;
        const isJob = ent === CONSTANTS.RESOURCES.jobs;

        if (isJob) {
            if (this.highlighted !== selected) {
                this.clearCurrent();
                this.highlighted = selected;
                this.applyHighlight(this.highlighted!, true);
                this.spawnIntegerTypeRings();
                this.setIntegerResourceHighlights(true);
                this.spawnRoleTypeLinesForSelectedImplementation();
                this.awaitingIntegerResources = true;
                this.selectedResourceIds.clear();
                this.clearConnections();
            }
            return;
        }

        // Clicked elsewhere: clear
        if (this.highlighted && selected !== this.highlighted) {
            // If awaiting integer resources and a resource was clicked, try to connect
            const selEnt = selected?.userData?.entity as string | undefined;
            if (this.awaitingIntegerResources && selEnt === CONSTANTS.RESOURCES.resources && this.highlighted) {
                const resId = (selected?.userData as { id?: string } | undefined)?.id ?? '';
                if (this.isIntegerResource(selected as THREE.Object3D) && this.isNumericResource(selected as THREE.Object3D) && resId && !this.selectedResourceIds.has(resId)) {
                    this.selectedResourceIds.add(resId);
                    const implObj = this.highlighted! as THREE.Object3D;
                    this.connectImplToResource(implObj, selected as THREE.Object3D);
                    // After selecting two, stop waiting and remove rings
                    if (this.selectedResourceIds.size >= 2) {
                        this.awaitingIntegerResources = false;
                        this.despawnIntegerTypeRings();
                    }
                    return; // keep sticky state
                }
            }
            // If not a valid resource click, clear sticky state
            this.clearCurrent();
        }
    }

    // Expose whether we're in sticky mode awaiting resource clicks
    isAwaitingResources(): boolean { return this.awaitingIntegerResources && !!this.highlighted; }

    // Handle a resource click without changing selection; returns true if consumed
    handleResourceClick(resObj: THREE.Object3D | null): boolean {
        if (!this.isAwaitingResources() || !resObj) return false;
        const selEnt = resObj.userData?.entity as string | undefined;
        if (selEnt !== CONSTANTS.RESOURCES.resources) return false;
        const resId = (resObj.userData as { id?: string } | undefined)?.id ?? '';
        if (!resId) return true; // consumed but nothing to do
        if (this.isIntegerResource(resObj) && this.isNumericResource(resObj) && !this.selectedResourceIds.has(resId)) {
            this.selectedResourceIds.add(resId);
            this.connectImplToResource(this.highlighted!, resObj);
            return true;
        }
        return true;
    }

    private clearCurrent() {
        if (!this.highlighted) return;
        this.applyHighlight(this.highlighted!, false);
        this.highlighted = null;
        // guide lines removed
        this.setIntegerResourceHighlights(false);
        this.despawnIntegerTypeRings();
        this.despawnRoleTypeLines();
        this.awaitingIntegerResources = false;
        this.selectedResourceIds.clear();
        this.clearConnections();
    }

    private applyHighlight(obj: THREE.Object3D, on: boolean) {
        const mesh = obj as THREE.Mesh;
        const mat = mesh.material as THREE.Material | THREE.Material[] | undefined;
        const setMat = (m: THREE.Material) => {
            const ud = (m.userData ??= {}) as { __implStickyEmissive?: number; __implStickyColor?: number };
            // Prefer emissive when available
            const maybeEmissive = (m as unknown as { emissive?: THREE.Color; emissiveIntensity?: number }).emissive;
            if (maybeEmissive instanceof THREE.Color) {
                if (ud.__implStickyEmissive === undefined) ud.__implStickyEmissive = maybeEmissive.getHex();
                maybeEmissive.set(on ? this.stickyEmissive : (ud.__implStickyEmissive ?? 0x000000));
                const ei = (m as unknown as { emissiveIntensity?: number });
                if (typeof ei.emissiveIntensity === 'number') ei.emissiveIntensity = on ? 3.0 : 1.0;
                return;
            }
            const maybeColor = (m as unknown as { color?: THREE.Color }).color;
            if (maybeColor instanceof THREE.Color) {
                if (ud.__implStickyColor === undefined) ud.__implStickyColor = maybeColor.getHex();
                maybeColor.set(on ? this.stickyEmissive : (ud.__implStickyColor ?? 0xffffff));
            }
        };

        if (Array.isArray(mat)) mat.forEach(setMat); else if (mat) setMat(mat);
    }

    // (guide lines feature removed)

    // (halo methods removed)

    // --- Integer resource highlighting ---
    private setIntegerResourceHighlights(_on: boolean) {
        // No-op for now: resource-level emissive highlighting disabled on request.
        return;
    }

    private isIntegerResource(child: THREE.Object3D): boolean {
        const resId = (child.userData as { id?: string } | undefined)?.id ?? '';
        const typeMatch = resId.match(/^(TYPE-[^/]+)/);
        const typeId = typeMatch?.[1] ?? '';
        if (!typeId) return false;
        const typeEntry = this.findTypeById(typeId);
        const typeName = String(typeEntry?.name ?? '');
        return this.isIntegerTypeName(typeName);
    }

    private isNumericResource(child: THREE.Object3D): boolean {
        const name = (child.userData as { name?: string } | undefined)?.name ?? '';
        if (typeof name !== 'string') return false;
        const trimmed = name.trim();
        return /^[-+]?\d+(?:\.\d+)?$/.test(trimmed);
    }

    // --- Integer type visual rings ---
    private spawnIntegerTypeRings() {
        this.despawnIntegerTypeRings();
        const group = new THREE.Group();
        group.name = 'impl-int-type-rings';

        const makeBoldRing = (center: THREE.Vector3, radius: number) => {
            const torus = new THREE.TorusGeometry(radius, 0.18, 12, 96);
            const mat = new THREE.MeshBasicMaterial({ color: this.guideColor, transparent: true, opacity: 0.9, depthTest: false, depthWrite: false });
            const ring = new THREE.Mesh(torus, mat);
            ring.position.copy(center);
            ring.rotation.x = Math.PI / 2;
            ring.renderOrder = 9999;
            return ring;
        };

        const getRadius = (mesh: THREE.Mesh) => {
            const geom = mesh.geometry as THREE.BufferGeometry | undefined;
            if (!geom) return 2.5;
            if (!geom.boundingSphere) geom.computeBoundingSphere();
            const r = geom.boundingSphere?.radius ?? 2.5;
            const s = mesh.scale as THREE.Vector3;
            const maxS = Math.max(Math.abs(s.x), Math.abs(s.y), Math.abs(s.z));
            return r * maxS + 1.0;
        };

        for (const t of this.getTypeMeshes()) {
            const typeName = String(t.name ?? '');
            if (!this.isIntegerTypeName(typeName)) continue;
            const mesh = t.mesh;
            const radius = getRadius(mesh);
            const ring = makeBoldRing(mesh.position.clone(), radius);
            group.add(ring);
        }

        if (group.children.length > 0) {
            this.scene.add(group);
            this.intRingsGroup = group;
        }
    }

    private despawnIntegerTypeRings() {
        if (!this.intRingsGroup) return;
        try { this.scene.remove(this.intRingsGroup); } catch { }
        this.intRingsGroup = null;
    }

    // --- Connections ---
    private connectImplToResource(implObj: THREE.Object3D, resObj: THREE.Object3D) {
        if (!this.connectionsGroup) {
            this.connectionsGroup = new THREE.Group();
            this.connectionsGroup.name = 'impl-resource-connections';
            this.scene.add(this.connectionsGroup);
        }
        // World positions
        const aWorld = new THREE.Vector3();
        const bWorld = new THREE.Vector3();
        (implObj as THREE.Object3D).getWorldPosition(aWorld);
        (resObj as THREE.Object3D).getWorldPosition(bWorld);

        // Radii (approx) from bounding spheres
        const getRadius = (m: THREE.Mesh) => {
            const g = m.geometry as THREE.BufferGeometry | undefined;
            if (!g) return 0.5;
            if (!g.boundingSphere) g.computeBoundingSphere();
            const r = g.boundingSphere?.radius ?? 0.5;
            const s = m.scale as THREE.Vector3;
            return r * Math.max(Math.abs(s.x), Math.abs(s.y), Math.abs(s.z));
        };
        const rImpl = getRadius(implObj as THREE.Mesh) + 0.12;
        const rRes = getRadius(resObj as THREE.Mesh) + 0.12;

        const dir = bWorld.clone().sub(aWorld).normalize();
        const start = aWorld.clone().add(dir.clone().multiplyScalar(rImpl));
        const end = bWorld.clone().add(dir.clone().multiplyScalar(-rRes));

        // Build a thick cylinder between start and end
        const mid = start.clone().add(end).multiplyScalar(0.5);
        const segLen = start.distanceTo(end);
        const cylGeom = new THREE.CylinderGeometry(0.06, 0.06, segLen, 12);
        const cylMat = new THREE.MeshBasicMaterial({ color: this.guideColor, transparent: true, opacity: 0.95, depthTest: false, depthWrite: false });
        const cyl = new THREE.Mesh(cylGeom, cylMat);
        cyl.position.copy(mid);
        const up = new THREE.Vector3(0, 1, 0);
        const quat = new THREE.Quaternion().setFromUnitVectors(up, end.clone().sub(start).normalize());
        cyl.quaternion.copy(quat);
        cyl.renderOrder = 9999;
        this.connectionsGroup.add(cyl);
    }

    private clearConnections() {
        if (!this.connectionsGroup) return;
        try { this.scene.remove(this.connectionsGroup); } catch { }
        this.connectionsGroup = null;
    }

    // --- Role type lines (job -> integer type) ---
    private spawnRoleTypeLinesForSelectedImplementation() {
        this.despawnRoleTypeLines();
        if (!this.highlighted) return;
        const impl = this.highlighted as THREE.Object3D;
        const implId = (impl.userData as { id?: string } | undefined)?.id ?? '';
        if (!implId) return;

        let inputTypeIds: string[] = [];
        let outputTypeIds: string[] = [];
        try {
            const res = (this.world as unknown as { getRoleTypeIdsForImplementation?: (id: string) => { inputTypeIds: string[]; outputTypeIds: string[] } })
                ?.getRoleTypeIdsForImplementation?.(implId);
            inputTypeIds = Array.isArray(res?.inputTypeIds) ? res!.inputTypeIds : [];
            outputTypeIds = Array.isArray(res?.outputTypeIds) ? res!.outputTypeIds : [];
        } catch { /* keep empty */ }

        const isIntegerTypeId = (typeId: string) => {
            const entry = this.findTypeById(typeId);
            const typeName = String(entry?.name ?? '');
            return this.isIntegerTypeName(typeName);
        };
        const inputIntId = inputTypeIds.find(isIntegerTypeId);
        const outputIntId = outputTypeIds.find(isIntegerTypeId);
        if (!inputIntId && !outputIntId) return;

        const group = new THREE.Group();
        group.name = 'impl-role-type-lines';

        const addBetween = (a: THREE.Object3D, b: THREE.Object3D, color: number) => {
            const aWorld = new THREE.Vector3();
            const bWorld = new THREE.Vector3();
            a.getWorldPosition(aWorld);
            b.getWorldPosition(bWorld);
            const getRadius = (m: THREE.Mesh) => {
                const g = m.geometry as THREE.BufferGeometry | undefined;
                if (!g) return 0.5;
                if (!g.boundingSphere) g.computeBoundingSphere();
                const r = g.boundingSphere?.radius ?? 0.5;
                const s = (m.scale as THREE.Vector3);
                return r * Math.max(Math.abs(s.x), Math.abs(s.y), Math.abs(s.z));
            };
            const rA = getRadius(a as THREE.Mesh) + 0.12;
            const rB = getRadius(b as THREE.Mesh) + 0.12;
            const dir = bWorld.clone().sub(aWorld).normalize();
            const start = aWorld.clone().add(dir.clone().multiplyScalar(rA));
            const end = bWorld.clone().add(dir.clone().multiplyScalar(-rB));
            const mid = start.clone().add(end).multiplyScalar(0.5);
            const segLen = start.distanceTo(end);
            const cylGeom = new THREE.CylinderGeometry(0.055, 0.055, segLen, 12);
            const cylMat = new THREE.MeshBasicMaterial({ color, transparent: true, opacity: 0.95, depthTest: false, depthWrite: false });
            const cyl = new THREE.Mesh(cylGeom, cylMat);
            cyl.position.copy(mid);
            const up = new THREE.Vector3(0, 1, 0);
            const quat = new THREE.Quaternion().setFromUnitVectors(up, end.clone().sub(start).normalize());
            cyl.quaternion.copy(quat);
            cyl.renderOrder = 9999;
            group.add(cyl);
        };

        // Use world's getMeshById if available; otherwise fall back to scene traversal
        const getMesh = (entity: string, id: string): THREE.Mesh | undefined => {
            try {
                const worldFn = (this.world as unknown as { getMeshById?: (entity: string, id: string) => THREE.Mesh | undefined })?.getMeshById;
                if (typeof worldFn === 'function') {
                    return worldFn.call(this.world, entity, id);
                }
            } catch { /* noop */ }
            return undefined;
        };

        if (inputIntId) {
            const tMesh = getMesh(CONSTANTS.ARCHETYPES.types, inputIntId);
            if (tMesh) addBetween(impl, tMesh, this.inputLineColor);
        }
        if (outputIntId) {
            const tMesh = getMesh(CONSTANTS.ARCHETYPES.types, outputIntId);
            if (tMesh) addBetween(impl, tMesh, this.outputLineColor);
        }

        if (group.children.length > 0) {
            this.scene.add(group);
            this.roleLinesGroup = group;
        }
    }

    private despawnRoleTypeLines() {
        if (!this.roleLinesGroup) return;
        try { this.scene.remove(this.roleLinesGroup); } catch { }
        this.roleLinesGroup = null;
    }
}

