'use client';
import type { WorkflowSpecJson } from '@toolproof-npm/schema';
import type { WorldInterface } from '@/explorer/worlds/_lib/types';
import type { InteractionContext } from '@/explorer/interactors/_lib/types';
import ExplorerHost from '@/explorer/ExplorerHost';
import { PersistentSelector } from '@/explorer/interactors/selectors';
import { SwitchingInteractor } from '@/explorer/interactors/SwitchingInteractor';
import { makeInteractorConfig } from '@/explorer/interactors/_lib/config';
import { makeCosmosConfig } from '@/explorer/worlds/cosmos/_lib/config';
import { makeExplorerConfig } from '@/explorer/_lib/config';
import { CosmosWorld } from '@/components/explorer/worlds/cosmos/CosmosWorld';
import type { CosmosWorldData } from '@/explorer/worlds/cosmos/_lib/types';
import * as THREE from 'three';
import { useMemo, useCallback, useRef, useEffect } from 'react';

interface CosmosLoaderProps {
    cosmosWorldData: CosmosWorldData;
    workflowSpec?: WorkflowSpecJson | null;
}

export default function CosmosLoader({ cosmosWorldData, workflowSpec }: CosmosLoaderProps) {

    // Stable function/object identities to avoid re-renders causing XR jank
    const predicate = useCallback((obj: THREE.Object3D) => {
        const isTarget = true;
        if (isTarget) {
            // console.log('Predicate found target:', obj.name, obj);
        }
        return isTarget;
    }, []);

    // Keep persistent selector (selection via click); hover highlights are handled separately in world logic
    const activeSelector = useMemo(() => new PersistentSelector(), []);

    const interactorFactory = useCallback((ctx: InteractionContext) => new SwitchingInteractor(ctx), []);

    const interactorConfig = useMemo(() =>
        makeInteractorConfig(
            { predicate, selector: activeSelector, interactorFactory },
            {
                // Optional per-page overrides go here
                // formatTest0TypesInnerHemisphere: false, // already default in baseInteractorConfig
            }
        ),
        [predicate, activeSelector, interactorFactory]
    );

    const cosmosConfig = useMemo(() => makeCosmosConfig(), []);

    const worldFactory = useCallback((scene: THREE.Scene, renderer: THREE.WebGLRenderer) => {
        const explorerConfig = makeExplorerConfig(interactorConfig, cosmosConfig);
        return new CosmosWorld(
            explorerConfig,
            scene,
            renderer
        );
    }, [interactorConfig, cosmosConfig]);

    const worldRef = useRef<WorldInterface | null>(null);
    useEffect(() => {
        const w = worldRef.current as { updateData?: (p: Partial<CosmosWorldData>) => void } | null;
        w?.updateData?.({ ...cosmosWorldData, workflowSpec });
    }, [cosmosWorldData, workflowSpec]);

    return (
        <ExplorerHost
            worldFactory={worldFactory}
            onWorldReady={(w) => { worldRef.current = w; }}
        />
    );

}
