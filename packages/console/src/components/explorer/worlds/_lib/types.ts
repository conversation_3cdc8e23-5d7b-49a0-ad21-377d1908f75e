import type { ExplorerConfig } from '@/explorer/_lib/types';
import type { InteractionView } from '@/explorer/interactors/_lib/types';
import * as THREE from 'three';


export interface RingConfig<T extends GeometryConfig> {
    isVisible: boolean;
    ringRadius: number;
    ringGuide: MeshConfig<LineConfig, LineMaterialConfig>;
    mesh: MeshConfig<T>;
}

export interface GeometryConfig {
    kind: 'box' | 'sphere' | 'panel' | 'line';
}

export interface SphereConfig extends GeometryConfig {
    kind: 'sphere';
    radius: number;
    widthSegments: number;
    heightSegments: number;
}

export interface BoxConfig extends GeometryConfig {
    kind: 'box';
    width: number;
    height: number;
    depth: number;
    widthSegments: number;
    heightSegments: number;
    depthSegments: number;
}

export interface PanelConfig extends GeometryConfig {
    kind: 'panel';
    width: number;
    height: number;
}

export interface LineConfig extends GeometryConfig {
    kind: 'line';
    segments: number;
}

export interface MaterialConfig {
    color: THREE.Color;
    metalness: number;
    roughness: number;
    emissive: THREE.Color;
    side: THREE.Side;
    transparent: boolean;
    opacity: number;
    depthWrite: boolean;
    depthTest: boolean;
}

export interface LineMaterialConfig extends MaterialConfig {
    linewidth: number;
}

export interface MeshConfig<T extends GeometryConfig, U = MaterialConfig> {
    isVisible: boolean;
    geometry: T;
    material: U;
}

export type NormalDirection = 'x' | 'y' | 'z';

export type OrientationMode = 'given' | 'radial' | 'tangent';

export type EntityMeshMapInner = Record<string, { name: string; description: string; mesh: THREE.Mesh }>;

export type EntityMeshMapOuter = Record<string, EntityMeshMapInner[]>;

export interface WorldInterface<Config = unknown, Data = unknown> {
    drawScene(): void;
    explorerConfig: ExplorerConfig<Config>;
    attachInteractionView(view: InteractionView): void;
    updateData: (payload: Partial<Data>) => void;
    updateOnHover: () => void;
    update?: (delta: number) => void; // Optional: for per-frame updates like animations
    dispose(): void;
    // Optional: worlds may expose role-to-type mapping for job-centric visuals
    getRoleTypeIdsForImplementation?: (jobId: string) => { inputTypeIds: string[]; outputTypeIds: string[] };
    // Optional: worlds may expose mesh lookup by entity name and id
    getMeshById?: (entity: string, id: string) => THREE.Mesh | undefined;
}

// Data-agnostic dummy visuals shared across worlds
export interface DummyConfig {
    dummiesEnabled: boolean;
    dummyIndicatorPoleHeight: number;
    dummyFlagColor: THREE.Color;
    nonDummyFlagColor: THREE.Color;
}

// Generic augmentation: clone-like dummies to reach totalCount.
export type DummyLinkOptions<T> = {
    // Candidate IDs from the related collection to link to (e.g., dummy formats/types/roles)
    linkIds?: string[];
    // Given the dummy index and candidate linkIds, return a shallow patch to merge into the dummy
    // For example: { formatId } or { typeId } or { resources: { inputs: [...], outputs: [...] } }
    getLinkPatch?: (idx: number, linkIds: string[]) => Partial<T>;
};

export interface WorldConfig {
    dummy: DummyConfig;
}
