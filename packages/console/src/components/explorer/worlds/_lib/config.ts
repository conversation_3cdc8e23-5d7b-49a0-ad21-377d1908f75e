import type { WorldConfig } from '@/components/explorer/worlds/_lib/types';
import * as THREE from 'three';


export const baseWorldConfig: WorldConfig = {
    dummy: {
        dummiesEnabled: false,
        dummyIndicatorPoleHeight: 1.0,
        dummyFlagColor: new THREE.Color(0x000000),
        nonDummyFlagColor: new THREE.Color(0xffffff),
    },
};

export function makeWorldConfig(overrides?: Partial<WorldConfig>): WorldConfig {
    return { ...baseWorldConfig, ...(overrides ?? {}) };
}
