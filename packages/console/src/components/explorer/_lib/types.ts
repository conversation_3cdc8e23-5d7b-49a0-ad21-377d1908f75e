import type { InteractorConfig } from '@/explorer/interactors/_lib/types';


// Unified top-level config object combining interactor + world-specific configuration.
// W is the world-specific config type (e.g., CosmosConfig). Additional loader/worlds can
// extend W with their own properties. This allows passing a single object through the world
// construction chain and keeps extension ergonomic.
export interface ExplorerConfig<W> {
    interactor: InteractorConfig;
    world: W;
}