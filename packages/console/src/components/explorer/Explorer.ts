import type { WorldInterface } from '@/explorer/worlds/_lib/types';
import { InteractionLayer } from '@/explorer/interactors/InteractionLayer';
import { createCamera } from '@/explorer/prefabs/camera';
import { createLights } from '@/explorer/prefabs/lights';
import { createScene } from '@/explorer/prefabs/scene';
import { createRenderer } from '@/explorer/systems/renderer';
import { createControls } from '@/explorer/systems/controls';
import { Resizer } from '@/explorer/systems/Resizer';
import * as THREE from 'three';


export default class Explorer {
    private world: WorldInterface;
    scene;
    renderer;
    camera;
    cameraRig = new THREE.Group();
    private clock = new THREE.Clock();
    private interactionLayer?: InteractionLayer;
    // Adapter: listen for generic interactor actions and route/log them here
    private interactorActionListener?: (e: Event) => void;

    constructor(container: HTMLDivElement, worldFactory: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => WorldInterface) {
        this.scene = createScene('skyblue'); // ATTENTION: should use config value
        this.renderer = createRenderer();
        const { ambientLight, mainLight } = createLights();
        this.camera = createCamera(30);
        this.cameraRig.add(this.camera);
        createControls(this.camera, this.renderer.domElement);
        this.world = worldFactory(this.scene, this.renderer);

        // Ensure we don't stack multiple canvases if Explorer is re-created (e.g., React StrictMode)
        try {
            while (container.firstChild) container.removeChild(container.firstChild);
        } catch { }
        container.append(this.renderer.domElement);
        new Resizer(container, this.camera, this.renderer);

        this.scene.add(ambientLight, mainLight, this.cameraRig);

        // Install a world-agnostic adapter for interactor actions
        this.interactorActionListener = (e: Event) => {
            try {
                const ce = e as CustomEvent;
                // For now: just log the action payload
                // Future: translate to world/explorer config changes (e.g., toggle role mode)
                // eslint-disable-next-line no-console
                console.log('[Explorer] interactor action:', ce.detail);
            } catch { /* ignore */ }
        };
        try { window.addEventListener('explorer-interactor-action', this.interactorActionListener as EventListener); } catch { }

        // Bind external InteractionLayer and attach view to world 
        this.interactionLayer = new InteractionLayer(this.world.explorerConfig.interactor);
        this.interactionLayer.bind({
            scene: this.scene,
            renderer: this.renderer,
            camera: this.camera,
            cameraRig: this.cameraRig,
            selector: this.world.explorerConfig.interactor.selector,
            filter: {
                predicate: this.world.explorerConfig.interactor.predicate,
                recursiveRaycast: this.world.explorerConfig.interactor.recursiveRaycast,
            },
        });
        this.world.attachInteractionView(this.interactionLayer);
    }

    async init() {
        // Build scene, but don't let failures prevent the render loop from starting
        try {
            // this.drawScene();

            // Position cameraRig (not just camera) so it works in both desktop and XR modes
            // In XR, the camera position is controlled by the headset relative to the rig
            // Moving the rig ensures both modes start at a good viewing distance
            this.cameraRig.position.set(0, 0, 150);
            this.camera.lookAt(0, 0, 0);
        } catch (err) {
            // Log but proceed to start the loop so background and future updates work
            console.error('[Explorer.init] createScene failed:', err);
        } finally {
            this.start();
        }
    }

    private start() {
        this.renderer.setAnimationLoop(() => {
            const dt = this.clock.getDelta();
            this.interactionLayer?.update(dt);
            // Let the world update animations and other per-frame logic
            try {
                this.world.update?.(dt);
            } catch { /* ignore */ }
            // Let the world update hover-driven visuals
            try {
                this.world.updateOnHover();
            } catch { /* ignore */ }
            this.renderer.render(this.scene, this.camera);
        });
    }

    // Expose world instance for loaders that need to push data updates later
    getWorld(): WorldInterface {
        return this.world;
    }

    // Convenience: forward a data payload to the world if it supports updates
    updateWorldData(payload: unknown): void {
        try {
            const w = this.world as WorldInterface & { updateData?: (p: unknown) => void };
            w.updateData?.(payload);
        } catch { /* ignore */ }
    }

    dispose(): void {
        try {
            // Stop render loop
            this.renderer.setAnimationLoop(null);
            } catch { }
        try {
            // Dispose interaction layer
            this.interactionLayer?.dispose();
            this.interactionLayer = undefined;
            } catch { }
        try {
            // Remove interactor action adapter
            if (this.interactorActionListener) {
                window.removeEventListener('explorer-interactor-action', this.interactorActionListener as EventListener);
                this.interactorActionListener = undefined;
            }
        } catch { }
        try {
            // Dispose world-owned resources
            this.world.dispose();
        } catch { }
        try {
            // Detach renderer canvas
            if (this.renderer?.domElement && this.renderer.domElement.parentElement) {
                this.renderer.domElement.parentElement.removeChild(this.renderer.domElement);
            }
        } catch { }
        try {
            // Dispose renderer and scene resources
            this.scene.traverse((obj: THREE.Object3D) => {
                const mesh = obj as THREE.Mesh;
                if (mesh && (mesh as unknown as { isMesh?: boolean }).isMesh) {
                    const geom = mesh.geometry as THREE.BufferGeometry | undefined;
                    const mat = mesh.material as THREE.Material | THREE.Material[] | undefined;
                    if (geom) geom.dispose?.();
                    if (Array.isArray(mat)) mat.forEach(m => m.dispose?.()); else mat?.dispose?.();
                }
            });
            this.renderer.dispose?.();
        } catch { }
    }

}