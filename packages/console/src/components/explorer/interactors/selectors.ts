import type { Selector, SelectionCommand } from '@/explorer/interactors/_lib/types';
import * as THREE from 'three';


export class TransientSelector implements Selector {
    onSelectStart(intersectedObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: intersectedObject, restoreOriginalPosition: true };
    }

    onSelectEnd(currentObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: null };
    }
}

export class PersistentSelector implements Selector {
    private lastSelectedObject: THREE.Object3D | null = null;

    onSelectStart(intersectedObject: THREE.Object3D | null): SelectionCommand {
        // Deselect if we click on the same object again or nothing
        if (!intersectedObject || intersectedObject === this.lastSelectedObject) {
            this.lastSelectedObject = null;
            return { selectedObject: null };
        }

        this.lastSelectedObject = intersectedObject;
        return { selectedObject: intersectedObject };
    }

    onSelectEnd(currentObject: THREE.Object3D | null): SelectionCommand {
        return { selectedObject: currentObject }; // no change
    }
}