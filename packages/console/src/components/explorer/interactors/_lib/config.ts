import type { InteractorConfig } from '@/explorer/interactors/_lib/types';


// Base (static) Explorer config. Dynamic React-provided deps (predicate, selector, interactorFactory)
// are injected by the factory below inside the component file.
export const baseInteractorConfig: Omit<InteractorConfig, 'predicate' | 'selector' | 'interactorFactory'> = {
        speedMultiplier: 5,
        skyColor: 'skyblue',
        laserColor: 'yellow',
        isGrabbable: false,
        recursiveRaycast: true,
};

export type InteractorDynamicDeps = Pick<InteractorConfig, 'predicate' | 'selector' | 'interactorFactory'>;

// Factory to compose final InteractorConfig in React-land while keeping this module React-free.
export function makeInteractorConfig(
    deps: InteractorDynamicDeps,
    overrides?: Partial<Omit<InteractorConfig, keyof InteractorDynamicDeps>>
): InteractorConfig {
    return { ...baseInteractorConfig, ...(overrides ?? {}), ...deps } as InteractorConfig;
}
