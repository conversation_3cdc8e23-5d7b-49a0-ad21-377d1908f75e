import * as THREE from 'three';


// Contract used by Explorer for user interaction in both XR and non-XR modes
export interface Interactor {
    // Update hover/select UI and highlighting
    updateInteraction(): void;
    // Update locomotion/rotation (XR typically; no-op for non-XR), given time delta in seconds
    updateMovement(delta: number): void;
    // Produce a raycast based on the active input device, returning the intersected object or null
    raycastFromController(): THREE.Object3D | null;
    // The most recent object under the cursor/laser (null when none)
    readonly intersectedObject: THREE.Object3D | null;
    // The object currently selected/active (null when none)
    readonly selectedObject: THREE.Object3D | null;
    // Cleanup any DOM or three.js resources/listeners
    dispose(): void;
}

// Factory signature for creating an Interactor instance
export type InteractorFactory = (ctx: InteractionContext) => Interactor;

// Shared interaction binding context used by all interactors and the InteractionLayer
export interface InteractionContext {
    scene: THREE.Scene;
    renderer: THREE.WebGLRenderer;
    camera: THREE.Camera;
    cameraRig: THREE.Group;
    selector: Selector;
    // Optional grouping for interaction filtering/raycast behavior
    filter?: {
        entities?: Set<string>;
        predicate?: (obj: THREE.Object3D) => boolean;
        recursiveRaycast?: boolean;
    };
}

// Minimal interaction surface worlds can query without owning input
export interface InteractionView {
    readonly selectedObject: THREE.Object3D | null;
    readonly intersectedObject: THREE.Object3D | null;
    setEntityFilter?(entities: Set<string>): void;
}

export interface XRManagerLike {
    addEventListener: (type: string, listener: () => void) => void;
    removeEventListener: (type: string, listener: () => void) => void;
    getSession: () => XRSession | null;
}

export interface InteractorConfig {
    speedMultiplier: number;
    skyColor: string;
    laserColor: string;
    predicate: (obj: THREE.Object3D) => boolean;
    isGrabbable: boolean;
    recursiveRaycast: boolean; // Flag to enable recursive raycasting
    selector: Selector;
    // Modernized: factory accepts plain context, not Explorer, to reduce coupling
    interactorFactory: (ctx: InteractionContext) => Interactor;
}

export interface SelectionCommand {
    selectedObject: THREE.Object3D | null;
    restoreOriginalPosition?: boolean;
}

export interface Selector {
    onSelectStart(intersectedObject: THREE.Object3D | null): SelectionCommand;
    onSelectEnd(currentObject: THREE.Object3D | null): SelectionCommand;
}


