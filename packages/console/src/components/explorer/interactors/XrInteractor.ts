import type { Interactor, InteractionContext, Selector } from '@/explorer/interactors/_lib/types';
import { applyHighlighting, computeDisplayText, raycastInteractive } from '@/explorer/interactors/_lib/utils';
import * as THREE from 'three';


export class XrInteractor implements Interactor {
    private scene: THREE.Scene;
    private camera: THREE.Camera;
    private renderer: THREE.WebGLRenderer;
    private cameraRig: THREE.Group;
    private selector: Selector;
    private controllerPrimary: THREE.Group; // preferred (right hand if available)
    private controllerSecondary: THREE.Group; // the other controller
    private activeController: THREE.Group; // currently used for raycasting
    private entities: Set<string> | undefined;
    private recursiveRaycast: boolean | undefined;
    private predicate: ((obj: THREE.Object3D) => boolean) | undefined;
    private _intersectedObject: THREE.Object3D | null = null;
    private _selectedObject: THREE.Object3D | null = null;
    private grabbedObjectOriginalPosition: THREE.Vector3 | null = null;
    private textSprite: THREE.Sprite | null = null;
    // Runtime speed boost controlled by right-hand buttons (0: slow, 1: fast, else: normal)
    private boostMultiplier = 1;
    // Edge-detect state for XR button toggles
    private prevRightButton2 = false;

    constructor(ctx: InteractionContext) {
        this.scene = ctx.scene;
        this.camera = ctx.camera;
        this.renderer = ctx.renderer;
        this.cameraRig = ctx.cameraRig;
        this.selector = ctx.selector;
        // Consume nested filter configuration
        this.entities = ctx.filter?.entities;
        this.predicate = ctx.filter?.predicate;
        this.recursiveRaycast = (ctx.filter?.recursiveRaycast ?? true);

        // Create both controllers; prefer right-hand when available
        this.controllerPrimary = this.renderer.xr.getController(1);
        this.controllerSecondary = this.renderer.xr.getController(0);

        // Default active controller to index 1 (common for right-hand), will switch on 'connected' events
        this.activeController = this.controllerPrimary;

        type XRHandedness = 'none' | 'left' | 'right';
        type ControllerWithMeta = THREE.Group & { userData: THREE.Group['userData'] & { handedness?: XRHandedness } };
        const primaryCtrl = this.controllerPrimary as ControllerWithMeta;
        const secondaryCtrl = this.controllerSecondary as ControllerWithMeta;

        const selectControllerForHandedness = () => {
            // Try to detect handedness metadata set by three.js on connect
            const primaryHS = primaryCtrl.userData?.handedness as XRHandedness | undefined;
            const secondaryHS = secondaryCtrl.userData?.handedness as XRHandedness | undefined;
            if (primaryHS === 'right') {
                this.activeController = this.controllerPrimary;
            } else if (secondaryHS === 'right') {
                this.activeController = this.controllerSecondary;
            } else if (primaryHS === 'left' && !secondaryHS) {
                // Only left present on primary
                this.activeController = this.controllerPrimary;
            } else {
                // Fallback: pick the first that has children (indicates an attached input source)
                this.activeController = (this.controllerPrimary.children.length > 0)
                    ? this.controllerPrimary
                    : this.controllerSecondary;
            }
        };

        type XRControllerEvent = Event & { data?: XRInputSource; target: ControllerWithMeta };
        const onConnected = (event: XRControllerEvent) => {
            try { if (event?.data) { event.target.userData.handedness = event.data.handedness as XRHandedness; } } catch { }
            selectControllerForHandedness();
        };
        const onDisconnected = () => { selectControllerForHandedness(); };
        // Three's XR controller objects expose 'connected'/'disconnected' custom events not in Object3DEventMap.
        // Use the underlying addEventListener with string cast to bypass type constraints.
        const attachRaw = (ctrl: THREE.Group, type: string, fn: EventListener) => {
            // Cast minimally for custom XR events not in Object3DEventMap
            (ctrl as unknown as { addEventListener: (t: string, l: EventListener) => void }).addEventListener(type, fn);
        };
        attachRaw(this.controllerPrimary, 'connected', onConnected as EventListener);
        attachRaw(this.controllerSecondary, 'connected', onConnected as EventListener);
        attachRaw(this.controllerPrimary, 'disconnected', onDisconnected as EventListener);
        attachRaw(this.controllerSecondary, 'disconnected', onDisconnected as EventListener);

        type AnyController = THREE.Group & { addEventListener: (type: string, listener: (event: unknown) => void) => void };
        const anyController = this.controllerPrimary as unknown as AnyController;
        anyController.addEventListener('selectstart', () => {
            const intersectedObject = this.raycastFromController();
            const normalized = this.resolveDisplayTarget(intersectedObject);
            const cmd = this.selector.onSelectStart(normalized);
            this._selectedObject = cmd.selectedObject;
            if (cmd.restoreOriginalPosition) {
                this.grabbedObjectOriginalPosition = this._selectedObject?.position.clone() ?? null;
            }
        });

        anyController.addEventListener('selectend', () => {
            const cmd = this.selector.onSelectEnd(this._selectedObject);
            if (cmd.restoreOriginalPosition && this._selectedObject) {
                this._selectedObject.position.copy(this.grabbedObjectOriginalPosition ?? new THREE.Vector3());
            }
            this._selectedObject = cmd.selectedObject;
            this.grabbedObjectOriginalPosition = null;
        });

        const laserGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(0, 0, 0),
            new THREE.Vector3(0, 0, -1)
        ]);

        // Laser color now expected to be encoded directly on a material or could be parameterized externally.
        // Fallback to a sensible default since config moved to renderer scope.
        const laserMaterial = new THREE.LineBasicMaterial({ color: 0xffaa00 });
        const laser1 = new THREE.Line(laserGeometry, laserMaterial);
        laser1.scale.z = 5; // make it 5 units long
        this.controllerPrimary.add(laser1);

        // Add a laser to secondary as well (dimmed)
        const laser2 = new THREE.Line(laserGeometry, new THREE.LineBasicMaterial({ color: 0x888888 }));
        laser2.scale.z = 5;
        this.controllerSecondary.add(laser2);

        this.cameraRig.add(this.controllerPrimary);
        this.cameraRig.add(this.controllerSecondary);

        // XR enabling and VRButton creation handled by SwitchingInteractor.

    }

    // Expose current state to composition users via readonly accessors
    get intersectedObject(): THREE.Object3D | null { return this._intersectedObject; }
    get selectedObject(): THREE.Object3D | null { return this._selectedObject; }

    dispose(): void {
        if (this.textSprite) {
            this.textSprite.parent?.remove(this.textSprite);
            this.textSprite.material.map?.dispose();
            this.textSprite.material.dispose();
            this.textSprite = null;
        }
    }

    updateMovement(delta: number): void {
        const session = this.renderer.xr.getSession();
        if (!session) return;

        // Effective movement speed (units/second): base from config, scaled by runtime boost
        // Config decoupled: use a default base speed with runtime boost.
        const movementSpeed = 8 * this.boostMultiplier;
        const rotationSpeed = 2;

        for (const inputSource of session.inputSources) {
            const gp = inputSource.gamepad;
            if (!gp || gp.axes.length < 2) continue;

            const x = gp.axes[2] ?? gp.axes[0];
            const y = gp.axes[3] ?? gp.axes[1];

            if (inputSource.handedness === 'left') {
                if (Math.abs(x) > 0.1 || Math.abs(y) > 0.1) {
                    const movement = new THREE.Vector3(x, 0, y)
                        .normalize()
                        .multiplyScalar(movementSpeed * delta)
                        .applyQuaternion(this.cameraRig.quaternion);
                    this.cameraRig.position.add(movement);
                }
                if (gp.buttons[0]?.pressed) {
                    this.cameraRig.position.y -= movementSpeed * delta;
                }
                if (gp.buttons[1]?.pressed) {
                    this.cameraRig.position.y += movementSpeed * delta;
                }
            } else if (inputSource.handedness === 'right') {
                if (Math.abs(x) > 0.1) {
                    const yaw = -x * rotationSpeed * delta;
                    const rotation = new THREE.Quaternion().setFromAxisAngle(new THREE.Vector3(0, 1, 0), yaw);
                    this.cameraRig.quaternion.multiply(rotation);
                }
                // Adjust runtime boost without overwriting the configured base multiplier
                if (gp.buttons[0]?.pressed) {
                    this.boostMultiplier = 0.1;   // slow
                } else if (gp.buttons[1]?.pressed) {
                    this.boostMultiplier = 10;    // fast
                } else {
                    this.boostMultiplier = 1;     // normal
                }

                // XR action: right controller button[2] issues a generic toggle-mode action
                const b2 = !!gp.buttons[2]?.pressed;
                if (b2 && !this.prevRightButton2) {
                    // Emit a generic input action so higher-level code (Explorer/world) can decide what to toggle.
                    // Keeping the interactor world-agnostic: no direct knowledge of "roles" or specific modes here.
                    try {
                        const ev = new CustomEvent('explorer-interactor-action', {
                            detail: {
                                action: 'toggle-mode',
                                requested: 'toggle',
                                source: 'xr',
                                device: 'right-controller',
                                button: 2,
                                edge: 'rising'
                            }
                        });
                        window.dispatchEvent(ev);
                    } catch { /* no-op */ }
                }
                this.prevRightButton2 = b2;
            }
        }
    }

    updateInteraction(): void {
        const intersectedObject = this.raycastFromController();
        this._intersectedObject = intersectedObject;
        applyHighlighting(this.scene, this._intersectedObject, this._selectedObject);
        const objectToDisplay = this.resolveDisplayTarget(this._selectedObject || this._intersectedObject);
        const text = computeDisplayText(objectToDisplay);
        this.showTextSprite(text);
    }

    raycastFromController(): THREE.Object3D | null {
        const raycaster = new THREE.Raycaster();
        const laserStart = new THREE.Vector3(0, 0, 0);
        const laserEnd = new THREE.Vector3(0, 0, -1);
        // Use the active controller determined from connected events
        const ctrl = this.activeController ?? this.controllerPrimary;
        ctrl.localToWorld(laserStart);
        ctrl.localToWorld(laserEnd);
        const direction = new THREE.Vector3().subVectors(laserEnd, laserStart).normalize();
        raycaster.ray.origin.copy(laserStart);
        raycaster.ray.direction.copy(direction);
        // Ensure sprites/labels can be intersected in XR
        (raycaster as unknown as { camera?: THREE.Camera }).camera = this.camera;
        const intersects = raycastInteractive(this.scene, raycaster, { entities: this.entities, recursive: !!this.recursiveRaycast, predicate: this.predicate });
        return intersects.length > 0 ? intersects[0].object : null;
    }

    // Optional setter to receive dynamic entity group filters from renderers
    setEntityFilter = (entities: Set<string>) => { this.entities = entities; };

    private showTextSprite(text: string) {
        // Remove old sprite
        if (this.textSprite) {
            this.textSprite.parent?.remove(this.textSprite);
            this.textSprite.material.map?.dispose();
            this.textSprite.material.dispose();
            this.textSprite = null;
        }
        if (!text || text.trim() === '') return;

        // Word-wrap the text into lines
        const fontSize = 24;
        const horizontalPadding = 40; // left/right padding inside panel
        const verticalPadding = 40;   // top/bottom padding inside panel
        const lineHeight = 28;
        const fixedCanvasWidth = 512; // keep a stable texture width for consistent scaling
        const maxWidth = fixedCanvasWidth - horizontalPadding;

        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d')!;
        tempCtx.font = `${fontSize}px sans-serif`;

        const words = text.split(' ');
        let line = '';
        const lines: string[] = [];
        for (let i = 0; i < words.length; i++) {
            const testLine = line + words[i] + ' ';
            const testWidth = tempCtx.measureText(testLine).width;
            if (testWidth > maxWidth && line !== '') {
                lines.push(line.trim());
                line = words[i] + ' ';
            } else {
                line = testLine;
            }
        }
        if (line !== '') lines.push(line.trim());

        const canvas = document.createElement('canvas');
        canvas.width = fixedCanvasWidth;
        const textBlockHeight = lines.length * lineHeight;
        canvas.height = textBlockHeight + verticalPadding + 20; // extra bottom margin for readability

        const ctx = canvas.getContext('2d')!;
        // Semi-transparent background panel (more see-through as requested)
        ctx.fillStyle = 'rgba(0,0,0,0.25)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        // Optional subtle border for contrast
        ctx.strokeStyle = 'rgba(255,255,255,0.35)';
        ctx.lineWidth = 2;
        ctx.strokeRect(1, 1, canvas.width - 2, canvas.height - 2);
        ctx.fillStyle = 'white';
        ctx.font = `${fontSize}px sans-serif`;
        ctx.textBaseline = 'top';
        ctx.textAlign = 'center';

        // Horizontal center
        const cx = canvas.width / 2;
        // Vertical start so block is vertically centered with padding
        const startY = (canvas.height - textBlockHeight) / 2;

        for (let i = 0; i < lines.length; i++) {
            ctx.fillText(lines[i], cx, startY + i * lineHeight);
        }

        const texture = new THREE.CanvasTexture(canvas);
        // Ensure sprite does not occlude underlying lines/groups via depth buffer while remaining translucent
        const material = new THREE.SpriteMaterial({
            map: texture,
            transparent: true,
            depthWrite: false, // do not write depth so underlying geometry can pass depth test
            depthTest: false,  // skip depth testing so we always blend over background
        });
        this.textSprite = new THREE.Sprite(material);
        const aspect = canvas.width / canvas.height;
        // Slightly smaller default scale for improved readability with transparent background
        this.textSprite.scale.set(1.3 * aspect, 1.3, 1);
        this.textSprite.position.set(0, 0, -5);
        const ctrl = this.activeController ?? this.controllerPrimary;
        ctrl.add(this.textSprite);
    }

    // Normalize display/selection to the nearest ancestor that carries our entity/userData
    private resolveDisplayTarget(obj: THREE.Object3D | null): THREE.Object3D | null {
        let cur: THREE.Object3D | null = obj;
        while (cur && !(cur.userData && typeof cur.userData === 'object' && 'entity' in cur.userData)) {
            cur = cur.parent as THREE.Object3D | null;
        }
        return cur ?? obj;
    }
}
