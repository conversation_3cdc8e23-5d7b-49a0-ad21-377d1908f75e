'use client';

import type { <PERSON>Id<PERSON><PERSON>, ExecutionId<PERSON>son, TypeMetaJson, RoleIdJson, RoleMapJson, ResourceIdJson, RoleLiteralJson, ExtractionSchemaValueJson } from '@toolproof-npm/schema';
import { JobSchema } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateResource } from '@toolproof-npm/validation';
import { LabeledInput } from '../_lib/LabeledInput';
import { getUiContext } from '@/builders/_lib/utils';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import SaveControls from '@/builders/_lib/SaveControls';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import { getNewId, uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useState, useEffect, useMemo } from 'react';
import type { ArchetypeMetaMap } from '@toolproof-npm/shared/types';

interface JobBuilderProps {
    typeMetaMap: ArchetypeMetaMap<TypeMetaJson>;
}

export default function JobBuilder({ typeMetaMap: injectedTypeMetaMap }: JobBuilderProps) {
    const [id, setId] = useState('');
    const [executionId, setExecutionId] = useState<string>('');
    const [name, setName] = useState('');
    const [description, setDescription] = useState<string>('dummy-description');
    const [uri, setUri] = useState<string>('');
    const [inputMap, setInputMap] = useState<RoleMapJson>({});
    const [outputMap, setOutputMap] = useState<RoleMapJson>({});
    const typeMetaMap = injectedTypeMetaMap;
    const typesMeta = typeMetaMap.specials;
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);
    const [newInputTypeId, setNewInputTypeId] = useState<string>('');
    const [newOutputTypeId, setNewOutputTypeId] = useState<string>('');
    const [job, setJob] = useState<unknown>('');
    const [jobText, setJobText] = useState<string>(JSON.stringify('', null, 2));
    const [jobParseError, setJobParseError] = useState<string | null>(null);


    // DOC: Fetch a new id for the job
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!id) {
                const newId = await getNewId(CONSTANTS.RESOURCES.resources); // ATTENTION
                setId(newId);
            }
        };
        asyncWrapper();
    }, [id]);

    // DOC: Fetch a new executionId for the job creation
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!executionId) {
                const newId = await getNewId(CONSTANTS.WORKFLOW.execution);
                setExecutionId(newId);
            }
        };
        asyncWrapper();
    }, [executionId]);

    // DOC: Initialize default selections for add-dropdowns when types load
    useEffect(() => {
        if (typesMeta?.length) {
            setNewInputTypeId((prev) => prev || typesMeta[0].id);
            setNewOutputTypeId((prev) => prev || typesMeta[0].id);
        }
    }, [typesMeta]);

    const uiContext = getUiContext();

    // DOC: Validate job against extractionSchema of selectedType
    const { isValid, errors: errors } = useMemo(() => {
        return validateResource(JobSchema as ExtractionSchemaValueJson, job); // ATTENTION
    }, [job]);

    // console.log('JobBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    // DOC: Add/Remove/Update role helpers for inputs and outputs
    const addRole = async (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        typeId: string,
    ) => {
        const roleId = await getNewId(CONSTANTS.ARCHETYPES_PSEUDO.roles) as RoleIdJson; // ATTENTION
        const newRole: RoleLiteralJson = {
            typeId: typeId as TypeIdJson,
            name: '',
            description: '',
        };
        setCurrent({ ...current, [roleId]: newRole as unknown as RoleMapJson[keyof RoleMapJson] }); // ATTENTION
    };

    // Update helpers for individual fields on a role literal
    const updateRoleType = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        roleId: RoleIdJson,
        typeId: TypeIdJson,
    ) => {
        const prev = current[roleId] as unknown as RoleLiteralJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [roleId]: { ...prev, typeId } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const updateRoleName = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        roleId: RoleIdJson,
        name: string,
    ) => {
        const prev = current[roleId] as unknown as RoleLiteralJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [roleId]: { ...prev, name } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const updateRoleDescription = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        roleId: RoleIdJson,
        description: string,
    ) => {
        const prev = current[roleId] as unknown as RoleLiteralJson | undefined;
        if (!prev) return;
        setCurrent({
            ...current,
            [roleId]: { ...prev, description } as unknown as RoleMapJson[keyof RoleMapJson],
        });
    };

    const removeRole = (
        current: RoleMapJson,
        setCurrent: React.Dispatch<React.SetStateAction<RoleMapJson>>,
        roleId: RoleIdJson,
    ) => {
        const { [roleId]: _removed, ...rest } = current;
        setCurrent(rest);
    };

    // DOC: Update job state on text change, with parse error handling
    const handleJobChange = (text: string) => {
        setJobText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setJobParseError('job must be a JSON object.');
                return;
            }
            setJob(parsed);
            setJobParseError(null);
        } catch (e) {
            setJobParseError((e as Error).message);
        }
    };

    // DOC: Auto-build job JSON whenever form fields change
    useEffect(() => {
        const jobObj = {
            identity: name, // ATTENTION: reusing name for identity for now
            name,
            description,
            uri,
            roles: {
                inputMap,
                outputMap,
            },
        };
        handleJobChange(JSON.stringify(jobObj, null, 2));
    }, [id, name, description, uri, executionId, inputMap, outputMap]);

    // DOC: Upload the job (as a resource) upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        const res = (await uploadResource(
            {
                id: id as ResourceIdJson,
                typeId: 'TYPE-Job',
                creationContext: {
                    roleId: CONSTANTS.SPECIALS.ROLE_BUILDER as RoleIdJson,
                    executionId: executionId as ExecutionIdJson
                },
            },
            JSON.stringify(job, null, 2)
        ));
        if (res.success) {
            const { storagePath } = res;
            setSaveStatus(`Saved. GCS: ${storagePath}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div className='p-6 max-w-5xl mx-auto space-y-4'>
            <form id='job-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {/* DOC: 'id' is generated server-side */}
                    <ReadOnlyIdField value={id} />
                </div>

                <LabeledInput
                    label='Name'
                    value={name}
                    onChange={setName}
                    placeholder={''}
                // error={isValidLocal.errors.name}
                />

                <LabeledInput label='Description' value={description} onChange={setDescription} placeholder='What this job is for' />

                <LabeledInput
                    label='Uri'
                    value={uri}
                    onChange={setUri}
                    placeholder={''}
                // error={isValidLocal.errors.uri}
                />

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    {/* Inputs */}
                    <fieldset className='border rounded p-3 space-y-3'>
                        <legend className='text-sm font-medium px-1'>Inputs</legend>
                        {
                            <>
                                <div className='flex items-center gap-2'>
                                    <select
                                        className='flex-1 rounded border border-gray-300 px-3 py-2'
                                        value={newInputTypeId}
                                        onChange={(e) => setNewInputTypeId(e.target.value)}
                                    >
                                        {typesMeta.map((t) => (
                                            <option key={t.id} value={t.id}>
                                                {t.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        type='button'
                                        className='px-3 py-2 bg-blue-600 text-white rounded disabled:opacity-50'
                                        disabled={!newInputTypeId}
                                        onClick={() => addRole(inputMap, setInputMap, newInputTypeId)}
                                    >
                                        Add input
                                    </button>
                                </div>

                                <ul className='divide-y border rounded'>
                                    {Object.entries(inputMap).length === 0 ? (
                                        <li className='p-2 text-sm text-gray-500'>No inputs yet</li>
                                    ) : (
                                        Object.entries(inputMap).map(([roleId, role]) => {
                                            const rl = role as unknown as RoleLiteralJson;
                                            return (
                                                <li key={roleId} className='p-2 flex flex-col gap-2'>
                                                    <div className='flex items-center gap-2'>
                                                        <span className='text-xs text-gray-500 shrink-0'>{roleId}</span>
                                                        <select
                                                            className='flex-1 rounded border border-gray-300 px-2 py-1'
                                                            value={rl?.typeId || ''}
                                                            onChange={(e) => updateRoleType(inputMap, setInputMap, roleId as RoleIdJson, e.target.value as TypeIdJson)}
                                                        >
                                                            {typesMeta.map((t) => (
                                                                <option key={t.id} value={t.id}>
                                                                    {t.name}
                                                                </option>
                                                            ))}
                                                        </select>
                                                        <button
                                                            type='button'
                                                            className='px-2 py-1 bg-gray-200 rounded hover:bg-gray-300'
                                                            onClick={() => removeRole(inputMap, setInputMap, roleId as RoleIdJson)}
                                                        >
                                                            Remove
                                                        </button>
                                                    </div>
                                                    <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role name'
                                                            value={rl?.name || ''}
                                                            onChange={(e) => updateRoleName(inputMap, setInputMap, roleId as RoleIdJson, e.target.value)}
                                                        />
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role description'
                                                            value={rl?.description || ''}
                                                            onChange={(e) => updateRoleDescription(inputMap, setInputMap, roleId as RoleIdJson, e.target.value)}
                                                        />
                                                    </div>
                                                </li>
                                            );
                                        })
                                    )}
                                </ul>
                            </>
                        }
                    </fieldset>

                    {/* Outputs */}
                    <fieldset className='border rounded p-3 space-y-3'>
                        <legend className='text-sm font-medium px-1'>Outputs</legend>
                        {
                            <>
                                <div className='flex items-center gap-2'>
                                    <select
                                        className='flex-1 rounded border border-gray-300 px-3 py-2'
                                        value={newOutputTypeId}
                                        onChange={(e) => setNewOutputTypeId(e.target.value)}
                                    >
                                        {typesMeta.map((t) => (
                                            <option key={t.id} value={t.id}>
                                                {t.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        type='button'
                                        className='px-3 py-2 bg-blue-600 text-white rounded disabled:opacity-50'
                                        disabled={!newOutputTypeId}
                                        onClick={() => addRole(outputMap, setOutputMap, newOutputTypeId)}
                                    >
                                        Add output
                                    </button>
                                </div>

                                <ul className='divide-y border rounded'>
                                    {Object.entries(outputMap).length === 0 ? (
                                        <li className='p-2 text-sm text-gray-500'>No outputs yet</li>
                                    ) : (
                                        Object.entries(outputMap).map(([roleId, role]) => {
                                            const rl = role as unknown as RoleLiteralJson;
                                            return (
                                                <li key={roleId} className='p-2 flex flex-col gap-2'>
                                                    <div className='flex items-center gap-2'>
                                                        <span className='text-xs text-gray-500 shrink-0'>{roleId}</span>
                                                        <select
                                                            className='flex-1 rounded border border-gray-300 px-2 py-1'
                                                            value={rl?.typeId || ''}
                                                            onChange={(e) => updateRoleType(outputMap, setOutputMap, roleId as RoleIdJson, e.target.value as TypeIdJson)}
                                                        >
                                                            {typesMeta.map((t) => (
                                                                <option key={t.id} value={t.id}>
                                                                    {t.name}
                                                                </option>
                                                            ))}
                                                        </select>
                                                        <button
                                                            type='button'
                                                            className='px-2 py-1 bg-gray-200 rounded hover:bg-gray-300'
                                                            onClick={() => removeRole(outputMap, setOutputMap, roleId as RoleIdJson)}
                                                        >
                                                            Remove
                                                        </button>
                                                    </div>
                                                    <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role name'
                                                            value={rl?.name || ''}
                                                            onChange={(e) => updateRoleName(outputMap, setOutputMap, roleId as RoleIdJson, e.target.value)}
                                                        />
                                                        <input
                                                            type='text'
                                                            className='rounded border border-gray-300 px-2 py-1'
                                                            placeholder='Role description'
                                                            value={rl?.description || ''}
                                                            onChange={(e) => updateRoleDescription(outputMap, setOutputMap, roleId as RoleIdJson, e.target.value)}
                                                        />
                                                    </div>
                                                </li>
                                            );
                                        })
                                    )}
                                </ul>
                            </>
                        }
                    </fieldset>
                </div>
            </form>

            <div>
                <h3 className='font-semibold mb-2'>Preview {id}</h3>
                <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>{loadingPreview
                    ? 'Loading…'
                    : JSON.stringify(
                        job,
                        null,
                        2)}
                </pre>
                <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
            </div>

            <SaveControls
                formId='job-form'
                buttonText='Save Job'
                disabled={!isValid || !id}
                isValid={isValid}
                invalidMessage='Fill all fields before saving.'
                error={error}
                saveStatus={saveStatus}
            />
        </div>
    );
}
