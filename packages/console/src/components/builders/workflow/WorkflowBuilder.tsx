'use client';

import type { Work<PERSON>tep<PERSON><PERSON>, BranchStep<PERSON><PERSON>, WorkflowSpec<PERSON>son, ResourceId<PERSON>son, WorkStepIdJson, ExecutionIdJson, RoleIdJson, RoleBindingMapJson, WorkflowIdJson, WorkflowSpecIdJson, JobJson } from '@toolproof-npm/schema';
import type { JobJsonWithId, SelectedIndex } from '@/builders/workflow/_lib/types';
import type { Role } from '@toolproof-npm/shared/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { getUiContext } from '@/builders/_lib/utils';
import WorkflowCanvas from '@/builders/workflow/WorkflowCanvas';
import WorkflowToolbar from '@/builders/workflow/WorkflowToolbar';
import WorkflowSidebar from '@/builders/workflow/WorkflowSidebar';
// Split context providers (selection, role/execution, resource data, bindings, drag)
import WorkflowProviders from '@/builders/workflow/contexts/WorkflowProviders';
import { getUnboundInputs } from '@/builders/workflow/_lib/utils';
import { useWorkflowSelection } from '@/builders/workflow/_lib/useWorkflowSelection';
import { collectJobIds, buildExecutionMap } from '@/builders/workflow/_lib/workflowTraversal';
import { useAutoBindOutputs } from '@/builders/workflow/_lib/useAutoBindOutputs';
import { useResourceBindings } from '@/builders/workflow/_lib/useResourceBindings';
import { runRemoteGraph } from '@/_lib/server/invokeRemoteGraph';
import { getNewId } from '@/_lib/server/firebaseAdminHelpers';
import { validateExecution, validateResourceMap } from '@toolproof-npm/validation';
import ExplorerEntry from '@/components/explorer/ExplorerEntry';
import CosmosLoader from '@/components/explorer/worlds/cosmos/CosmosLoader';
import { uploadResource } from '@/_lib/server/firebaseAdminHelpers';
import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import type { CosmosWorldData } from '@/explorer/worlds/cosmos/_lib/types';
import type { DragSource } from '@/builders/workflow/_lib/types';

interface WorkflowBuilderProps {
    cosmosWorldData: CosmosWorldData;
}

export default function WorkflowBuilder({ cosmosWorldData }: WorkflowBuilderProps) {
    const [showExplorer, setShowExplorer] = useState(false);

    const [workflowSpec, setWorkflowSpec] = useState<WorkflowSpecJson | null>(null);
    const [workflowSpecId, setWorkflowSpecId] = useState<WorkflowSpecIdJson | null>(null);
    const [workflowId, setWorkflowId] = useState<WorkflowIdJson | null>(null);
    const [workflowName, setWorkflowName] = useState('Untitled Workflow'); // ATTENTION: Workflow is not a Documented

    const resourceDataMap = cosmosWorldData.resourceDataMap;

    // console.log('resourceDataMap:', JSON.stringify(resourceDataMap, null, 2));

    // Build jobs map from ResourceDataMap['TYPE-Job']
    const jobDataMap = useMemo(() => {
        // DOC: jobs are resources under the special type 'TYPE-Job'
        const jobResources = resourceDataMap[CONSTANTS.SPECIALS.TYPE_Job] ?? [];
        return jobResources.reduce((map, res) => {
            const job = res.extractedData as unknown as JobJson;
            map.set(res.id as ResourceIdJson, { ...job, id: res.id as ResourceIdJson } as JobJsonWithId);
            return map;
        }, new Map<ResourceIdJson, JobJsonWithId>());
    }, [resourceDataMap]);

    const [workStepMap, setWorkStepMap] = useState<Map<string, WorkStepJson>>(new Map()); // ATTENTION: is workStepMap still needed?

    // DOC: Track selected step (and optional branch case) for StepPanel/RoleBindingPanel
    const [selectedIndex, setSelectedIndex] = useState<SelectedIndex | null>(null);

    const { activeStep, activeExecution, activeJob } = useWorkflowSelection(workflowSpec, selectedIndex, jobDataMap);

    // Global drag source state (shared between RoleBindingPanel and ResourceMapsPanel)
    const [dragSource, setDragSource] = useState<DragSource | null>(null);

    // Build a global role map across ALL jobs referenced in the workflow
    const roleMap = useMemo(() => {
        const map = new Map<string, Role>();
        const jobIds = collectJobIds(workflowSpec);
        for (const jId of jobIds) {
            const job = jobDataMap.get(jId);
            if (!job) continue;
            Object.entries(job.roles.inputMap ?? {}).forEach(([rid, role]) => map.set(rid, { id: rid as RoleIdJson, ...role } as Role));
            Object.entries(job.roles.outputMap ?? {}).forEach(([rid, role]) => map.set(rid, { id: rid as RoleIdJson, ...role } as Role));
        }
        return map;
    }, [workflowSpec, jobDataMap]);

    // For branch steps: compute sibling case step ids to exclude from resource panel
    const excludeCaseStepIds = useMemo(() => {
        if (!activeStep || activeStep.kind !== CONSTANTS.STEP.branch || !selectedIndex) return [] as string[];
        const branch = activeStep as BranchStepJson;
        const cases = branch.cases ?? [];
        const rawIdx = selectedIndex.caseIndex ?? 0;
        const caseIdx = Math.max(0, Math.min(rawIdx, Math.max(cases.length - 1, 0)));
        return cases
            .map(cw => (cw?.what?.id as string | undefined))
            .filter((id, idx) => typeof id === 'string' && idx !== caseIdx) as string[];
    }, [activeStep, selectedIndex]);

    // Build a global execution map for classifying resources (input/output) by their origin
    const executionMap = useMemo(() => buildExecutionMap(workflowSpec), [workflowSpec]);

    // DOC: Generate workflowSpecId and workflowId if not present
    useEffect(() => {

        const asyncWrapper = async () => {
            // Ensure we have a workflow-spec id
            if (!workflowSpecId) {
                const id = await getNewId(CONSTANTS.WORKFLOW.workflowSpec) as WorkflowSpecIdJson;
                setWorkflowSpecId(id);
            }

            // Ensure we have a workflow id as well
            if (!workflowId) {
                const id = await getNewId(CONSTANTS.WORKFLOW.workflow) as WorkflowIdJson;
                setWorkflowId(id);
            }
        };

        asyncWrapper();

    }, [workflowSpecId, workflowId]);

    // DOC: When both ids are available, initialize workflowSpec
    useEffect(() => {
        let mounted = true;
        if (workflowSpecId && workflowId) {
            // Only set if not already set or if ids don't match
            setWorkflowSpec((prev) => {
                if (!mounted) return prev;
                if (!prev || prev.id !== workflowSpecId || prev.workflow?.id !== workflowId) {
                    return {
                        id: workflowSpecId,
                        workflow: {
                            id: workflowId,
                            steps: []
                        },
                        resourceMaps: [{}]
                    } as WorkflowSpecJson;
                }
                return prev;
            });
        }
        return () => {
            mounted = false;
        };
    }, [workflowSpecId, workflowId, setWorkflowSpec]);

    // Auto-bind outputs when all inputs are bound
    useAutoBindOutputs({ workflowSpec, activeExecution, jobDataMap, setWorkflowSpec });

    // Resource binding helpers
    const { bindInputPath, bindInputPointer, clearInputBinding } = useResourceBindings({ workflowSpec, activeExecution, setWorkflowSpec });

    // DOC: Helper function to construct a WorkStep object from an jobMetaJson (does not append to workflow.steps)
    const makeWorkStepFromJob = useCallback(async (job: JobJsonWithId): Promise<WorkStepJson> => {
        if (!workflowSpec) throw new Error('workflowSpec not initialized');
        if (activeExecution) {
            const unbound = getUnboundInputs(activeExecution, workflowSpec.resourceMaps?.[0]);
            if (unbound.length > 0) {
                alert(`Provide inputs for the current step before adding another. Missing: ${unbound.join(', ')}`);
                throw new Error('unbound inputs');
            }
        }

        const workStepId = await getNewId(CONSTANTS.STEP.work) as WorkStepIdJson;
        const executionId = workStepId.replace(CONSTANTS.STEP.work.toUpperCase(), CONSTANTS.WORKFLOW.execution.toUpperCase()) as ExecutionIdJson; // ATTENTION

        const inputBindingMap: RoleBindingMapJson = {};
        const inputs = job.roles.inputMap;
        Object.keys(inputs).forEach((roleId) => {
            const resourceId = 'RESOURCE-X'; // DOC: Placeholder until user binds input
            inputBindingMap[roleId as RoleIdJson] = resourceId;
        });

        const outputBindingMap: RoleBindingMapJson = {};
        const outputs = job.roles.outputMap;
        Object.keys(outputs).forEach(async (roleId) => {
            const resourceId = await getNewId(CONSTANTS.RESOURCES.resources) as ResourceIdJson;
            outputBindingMap[roleId as RoleIdJson] = resourceId;
        });

        const newWorkStep: WorkStepJson = {
            id: workStepId,
            kind: CONSTANTS.STEP.work,
            execution: {
                id: executionId,
                jobId: job.id as ResourceIdJson,
                roleBindings: {
                    inputBindingMap: inputBindingMap,
                    outputBindingMap: outputBindingMap
                }
            }
        };

        return newWorkStep;

    }, [activeExecution, workflowSpec]);

    // DOC: Handler for dropping an job onto the 'WorkStep' area of the WorkflowCanvas
    const onDropWorkStep = useCallback(async (job: JobJsonWithId) => {
        try {
            const newWorkStep = await makeWorkStepFromJob(job);
            setWorkflowSpec((prev) => {
                const base = (prev ?? {
                    id: workflowSpecId ?? '',
                    workflow: { id: workflowId ?? '', steps: [] },
                    resourceMaps: []
                }) as WorkflowSpecJson;
                return {
                    ...base,
                    workflow: { ...base.workflow, steps: [...base.workflow.steps, newWorkStep] }
                };
            });
            setWorkStepMap(prev => new Map(prev).set(newWorkStep.id ?? '', newWorkStep));
            setSelectedIndex((prev) => (prev == null ? { stepIndex: 0, caseIndex: null } : { stepIndex: prev.stepIndex + 1, caseIndex: null }));
        } catch (err) {
            // makeWorkStepFromJob already alerts for input binding issues; ignore errors
            console.warn('Failed to create workStep:', err);
        }
    }, [makeWorkStepFromJob, workflowSpecId, workflowId]);

    // DOC: Handler for dropping an job onto the 'BranchStep' area of the WorkflowCanvas
    const onDropBranchStep = useCallback(async (job: JobJsonWithId) => {
        console.log('onDropBranchStep is not yet implemented');
        return;
    }, []);

    // DOC: Handler for dropping an job onto the 'Add Case' area of a BranchStepTile
    const onAddBranchStepCase = useCallback((jobId: ResourceIdJson) => {
    }, []);

    // DOC: Handler for dropping an job onto the 'WhileStep' area of the WorkflowCanvas
    const onDropWhileStep = useCallback(async (job: JobJsonWithId) => {
        console.log('onDropWhileStep is not yet implemented');
        return;
    }, []);

    // DOC: Handler for dropping an job onto the 'ForStep' area of the WorkflowCanvas
    const onDropForStep = useCallback(async (job: JobJsonWithId) => {
        console.log('onDropForStep is not yet implemented');
        return;
    }, []);

    // DOC: Handler for clicking an job in WorkflowCanvas (currently just logs the job, can be expanded to show job details)
    const handleJobClick = useCallback(async (job: JobJsonWithId) => {
        console.log('Job clicked:', job.name);
    }, []);

    // DOC: Allow clicking a step in StepPanel to change selection
    const handleSelectStep = useCallback((index: SelectedIndex) => {
        if (!workflowSpec) return;
        setSelectedIndex(index);
    }, [workflowSpec]);

    // DOC: Handler for dispatching workflowSpec to the Engine for execution (currently it's executed directly, but in the future it will be scheduled)
    const handleDispatchWorkflowSpec = useCallback(async () => {
        if (!workflowSpec) return;
        // console.log('Dispatching workflowSpec:', JSON.stringify(workflowSpec, null, 2));
        await runRemoteGraph(workflowSpec);
        setShowExplorer(true);
        return;

    }, [workflowSpec]);

    // DOC: Handler for binding an inputRole to a file path
    // Wire hook fns to legacy names for downstream components
    const onBindInputPath = bindInputPath;
    const onBindInputPointer = bindInputPointer;
    const onClearInputBinding = clearInputBinding;

    // Memoized slices for split contexts
    const selectionValue = useMemo(() => ({
        activeStep,
        activeExecution,
        activeJob,
        selectedIndex,
        excludeCaseStepIds
    }), [activeStep, activeExecution, activeJob, selectedIndex, excludeCaseStepIds]);

    const dragValue = useMemo(() => ({ dragSource, setDragSource }), [dragSource, setDragSource]);

    const resourceDataValue = useMemo(() => ({
        resourceDataMap,
        resourceMap: (workflowSpec?.resourceMaps?.[0] ?? {})
    }), [resourceDataMap, workflowSpec?.resourceMaps]);

    const bindingsValue = useMemo(() => ({
        onBindInputPath,
        onBindInputPointer,
        onClearInputBinding
    }), [onBindInputPath, onBindInputPointer, onClearInputBinding]);

    const roleExecutionValue = useMemo(() => ({ roleMap, executionMap }), [roleMap, executionMap]);

    if (!workflowSpec) {
        return null;
    }

    const { isValid: isValidExecution, errors: errorsExecution } = validateExecution(activeExecution, activeJob, getUiContext());

    const { isValid: isValidResourceMap, errors: errorsResourceMap } = validateResourceMap(workflowSpec.resourceMaps[0], activeExecution, getUiContext());

    // console.log('Execution validation:', isValidExecution, errorsExecution);
    // console.log('ResourceMap validation:', isValidResourceMap, errorsResourceMap);

    return ( // ATTENTION: consider React.context
        <div className="h-screen flex flex-col bg-gray-50">
            {/* Toolbar */}
            <WorkflowToolbar
                workflowName={workflowName}
                onWorkflowNameChange={setWorkflowName}
                onDispatch={handleDispatchWorkflowSpec}
                onViewXr={() => setShowExplorer(!showExplorer)}
                validationResult={null} // ATTENTION: should apply to validation of the whole WorkflowSpec
            />

            <div className="flex-1 flex overflow-hidden">
                <div className="flex-1 relative">
                    {showExplorer ? (
                        <ExplorerEntry Loader={CosmosLoader} loaderProps={{ cosmosWorldData, workflowSpec }} />
                    ) : (
                        <WorkflowCanvas
                            jobMap={jobDataMap}
                            onJobClick={handleJobClick}
                        />
                    )}
                </div>
                {!showExplorer && (
                    <WorkflowProviders
                        selection={selectionValue}
                        roleExecution={roleExecutionValue}
                        resourceData={resourceDataValue}
                        bindings={bindingsValue}
                        drag={dragValue}
                    >
                        <WorkflowSidebar
                            steps={workflowSpec.workflow.steps}
                            workStepMap={workStepMap}
                            jobDataMap={jobDataMap}
                            selectedIndex={selectedIndex}
                            onSelectStep={handleSelectStep}
                            onAddBranchStepCase={onAddBranchStepCase}
                        />
                    </WorkflowProviders>
                )}
            </div>
            {!showExplorer && (
                <div className="bg-white border-t border-gray-200">
                    <div className="max-w-full mx-auto px-4 py-3">
                        <div className="grid grid-cols-4 gap-4">
                            {['WorkStep', 'BranchStep', 'WhileStep', 'ForStep'].map((name) => {
                                return (
                                    <div
                                        key={name}
                                        onDragOver={(e) => e.preventDefault()}
                                        onDrop={async (e) => {
                                            e.preventDefault();
                                            const jobId = e.dataTransfer?.getData('application/toolproof-job-id') as ResourceIdJson;
                                            if (!jobId) return;
                                            const job = jobDataMap.get(jobId) as JobJsonWithId;
                                            if (!job) {
                                                console.warn('Dropped job id not found:', jobId);
                                                return;
                                            }
                                            if (name === 'WorkStep') {
                                                // Reuse existing click logic when dropping onto WorkStep
                                                await onDropWorkStep(job as JobJsonWithId);
                                            } else if (name === 'BranchStep') {
                                                await onDropBranchStep(job as JobJsonWithId);
                                            } else if (name === 'WhileStep') {
                                                await onDropWhileStep(job as JobJsonWithId);
                                            } else if (name === 'ForStep') {
                                                await onDropForStep(job as JobJsonWithId);
                                            } else {
                                                console.log(`Canceling step creation for job ${job.name}`);
                                            }
                                        }}
                                        className="min-h-[64px] flex items-center justify-center rounded border border-dashed border-gray-300 bg-gray-50 p-3"
                                    >
                                        <div className="text-sm font-medium text-gray-700">{name}</div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );

}
