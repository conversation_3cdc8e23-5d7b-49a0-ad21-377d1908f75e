import type { <PERSON><PERSON>d<PERSON><PERSON>, RoleId<PERSON>son, ExecutionId<PERSON>son, ExecutionJson, ResourceMapJson, JobJson, RoleBindingMapJson, ResourceDataJson, ResourcePotentialInputJson, ResourcePotentialOutputJson } from '@toolproof-npm/schema';


// Ensure nested execution map entry exists
export function ensureExecution(resourceMap: ResourceMapJson, executionId: ExecutionIdJson) {
    if (!resourceMap[executionId]) {
        resourceMap[executionId] = {} as Record<RoleIdJson, ResourceDataJson | ResourcePotentialInputJson | ResourcePotentialOutputJson>;
    }
    return resourceMap[executionId];
}

function getUnboundRoles(roleBindingMap: RoleBindingMapJson, execution: ExecutionJson, resourceMap: ResourceMapJson) {
    const unboundKeys: string[] = [];
    const execBucket = resourceMap[execution.id] || {};
    for (const roleKey of Object.keys(roleBindingMap)) {
        if (!(roleKey in execBucket)) {
            unboundKeys.push(roleKey);
        }
    }
    return unboundKeys;
}

export function getUnboundInputs(execution: ExecutionJson, resourceMap: ResourceMapJson) {
    return getUnboundRoles(execution.roleBindings.inputBindingMap, execution, resourceMap);
}

export function getUnboundOutputs(execution: ExecutionJson, resourceMap: ResourceMapJson) {
    return getUnboundRoles(execution.roleBindings.outputBindingMap, execution, resourceMap);
}

// Produce nested outputs under executionId -> roleId
export function bindOutputs(execution: ExecutionJson, job: JobJson): ResourceMapJson {
    const bound: ResourceMapJson = {} as ResourceMapJson;
    bound[execution.id] = {} as Record<RoleIdJson, ResourcePotentialOutputJson>;
    const outputBindingMap = execution.roleBindings.outputBindingMap;
    for (const [roleId, resourceId] of Object.entries(outputBindingMap)) {
        const role = job.roles.outputMap?.[roleId as RoleIdJson];
        if (!role) throw new Error(`Cannot bind output for roleId ${roleId}: role not found`);
        (bound[execution.id] as Record<RoleIdJson, ResourcePotentialOutputJson>)[roleId as RoleIdJson] = {
            id: resourceId,
            typeId: role.typeId as TypeIdJson,
            creationContext: { roleId: roleId as RoleIdJson, executionId: execution.id },
            kind: 'potential-output'
        };
    }
    return bound;
}

// Resolve a resource chain by following potential-input pointers.
// Stops when encountering a realized entry or a potential-output.
export type ResourceSocket = { executionId: ExecutionIdJson; roleId: RoleIdJson };

export type ResolveResult =
    | { status: 'realized'; entry: ResourceDataJson; path: ResourceSocket[] }
    | { status: 'blocked-output'; entry: ResourcePotentialOutputJson; path: ResourceSocket[] }
    | { status: 'unresolved'; reason: 'missing-entry' | 'cycle' | 'depth-exceeded'; path: ResourceSocket[] };

export function resolveResourceChain(
    resourceMap: ResourceMapJson,
    start: ResourceSocket,
    opts?: { maxDepth?: number }
): ResolveResult {
    const maxDepth = opts?.maxDepth ?? 50;
    const visited = new Set<string>();
    const path: ResourceSocket[] = [];
    let current: ResourceSocket = start;

    for (let depth = 0; depth <= maxDepth; depth++) {
        path.push(current);
        const visitKey = `${current.executionId}::${current.roleId}`;
        if (visited.has(visitKey)) {
            return { status: 'unresolved', reason: 'cycle', path };
        }
        visited.add(visitKey);

        const bucket = resourceMap[current.executionId];
        if (!bucket) return { status: 'unresolved', reason: 'missing-entry', path };
        const entry = bucket[current.roleId] as (
            | (ResourceDataJson & { kind: 'realized' })
            | (ResourcePotentialInputJson & { kind: 'potential-input' })
            | (ResourcePotentialOutputJson & { kind: 'potential-output' })
            | undefined
        );
        if (!entry) return { status: 'unresolved', reason: 'missing-entry', path };

        if (entry.kind === 'realized') {
            return { status: 'realized', entry: entry as ResourceDataJson, path };
        }
        if (entry.kind === 'potential-output') {
            return { status: 'blocked-output', entry: entry as ResourcePotentialOutputJson, path };
        }
        // potential-input: follow pointer backwards
        if (entry.kind === 'potential-input') {
            const pointer = (entry as ResourcePotentialInputJson).pendingRef as ResourceSocket | undefined;
            if (!pointer) return { status: 'unresolved', reason: 'missing-entry', path };
            current = pointer;
            continue;
        }

        // Unknown case
        return { status: 'unresolved', reason: 'missing-entry', path };
    }
    return { status: 'unresolved', reason: 'depth-exceeded', path };
}
