import { useCallback } from 'react';
import type { WorkflowSpecJson, ExecutionJson, ResourceMap<PERSON>son, RoleIdJson, ResourceDataJson, ExecutionIdJson, ResourcePotentialInputJson } from '@toolproof-npm/schema';
import { ensureExecution, resolveResourceChain } from '@/builders/workflow/_lib/utils';

interface Params {
    workflowSpec: WorkflowSpecJson | null;
    activeExecution: ExecutionJson | null;
    setWorkflowSpec: React.Dispatch<React.SetStateAction<WorkflowSpecJson | null>>;
}

export function useResourceBindings({ workflowSpec, activeExecution, setWorkflowSpec }: Params) {
    const bindInputPath = useCallback((roleId: RoleIdJson, resourceData: ResourceDataJson) => {
        if (!workflowSpec) throw new Error('workflowSpec not found');
        if (!activeExecution) throw new Error('activeExecution not found');
        setWorkflowSpec(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.resourceMaps[0] || {}) } as ResourceMapJson;
            const bucket = ensureExecution(resMap, activeExecution.id as ExecutionIdJson);
            activeExecution.roleBindings.inputBindingMap[roleId] = resourceData.id;
            bucket[roleId] = resourceData;
            return { ...prev, resourceMaps: [resMap] };
        });
    }, [workflowSpec, activeExecution, setWorkflowSpec]);

    const bindInputPointer = useCallback((roleId: RoleIdJson, source: { executionId: ExecutionIdJson; roleId: RoleIdJson }) => {
        if (!workflowSpec) throw new Error('workflowSpec not found');
        if (!activeExecution) throw new Error('activeExecution not found');
        const targetExecId = activeExecution.id as ExecutionIdJson;
        const resMap0 = (workflowSpec.resourceMaps?.[0] ?? {}) as ResourceMapJson;
        const sourceEntry = resMap0?.[source.executionId]?.[source.roleId] as (ResourceDataJson | ResourcePotentialInputJson | undefined);
        if (!sourceEntry) throw new Error(`resourceEntry not found for source (${source.executionId}, ${source.roleId})`);
        setWorkflowSpec(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.resourceMaps[0] || {}) } as ResourceMapJson;
            const bucket = ensureExecution(resMap, targetExecId);
            // Try to resolve the chain starting from the source socket
            const result = resolveResourceChain(resMap, { executionId: source.executionId, roleId: source.roleId });
            if (result.status === 'realized') {
                const realized = result.entry;
                activeExecution.roleBindings.inputBindingMap[roleId] = realized.id;
                bucket[roleId] = realized;
            } else {
                // Fallback to potential-input pointing backward
                const potentialInput: ResourcePotentialInputJson = {
                    id: sourceEntry.id,
                    typeId: sourceEntry.typeId,
                    creationContext: { roleId, executionId: targetExecId },
                    kind: 'potential-input',
                    pendingRef: { executionId: source.executionId, roleId: source.roleId }
                };
                activeExecution.roleBindings.inputBindingMap[roleId] = potentialInput.id;
                bucket[roleId] = potentialInput;
            }
            return { ...prev, resourceMaps: [resMap] };
        });
    }, [workflowSpec, activeExecution, setWorkflowSpec]);

    const clearInputBinding = useCallback((roleId: RoleIdJson) => {
        if (!activeExecution) throw new Error('activeExecution not found');
        setWorkflowSpec(prev => {
            if (!prev) return prev;
            const resMap = { ...(prev.resourceMaps[0] || {}) } as ResourceMapJson;
            const bucket = resMap[activeExecution.id];
            if (bucket && roleId in bucket) {
                delete bucket[roleId];
            }
            return { ...prev, resourceMaps: [resMap], workflow: { ...prev.workflow } };
        });
    }, [activeExecution, setWorkflowSpec]);

    return { bindInputPath, bindInputPointer, clearInputBinding };
}
