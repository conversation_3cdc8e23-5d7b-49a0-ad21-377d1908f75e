import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Execution<PERSON>d<PERSON><PERSON>, RoleIdJson } from '@toolproof-npm/schema';


export type  JobJsonWithId = JobJson & { id: ResourceIdJson };


// UI helper types (local)
export type SelectedIndex = {
    // Index into workflowSpec.workflow.steps
    stepIndex: number;
    // For BranchStep only; otherwise has no meaning
    caseIndex: number | null;
};


export interface DragSource {
    executionId: ExecutionIdJson;
    roleId: RoleIdJson;
}