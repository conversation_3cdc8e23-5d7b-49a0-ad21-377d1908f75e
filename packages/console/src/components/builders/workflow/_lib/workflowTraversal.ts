import type { WorkflowSpec<PERSON><PERSON>, Execution<PERSON>son, ExecutionId<PERSON>son, ResourceIdJson, WorkStepJson, BranchStepJson, WhileStepJson, ForStepJson } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';

// Collect all executions from the workflow spec (including nested wrappers)
export function collectExecutions(spec: WorkflowSpecJson | null): ExecutionJson[] {
  if (!spec) return [];
  const executions: ExecutionJson[] = [];
  for (const step of spec.workflow.steps) {
    if (!step) continue;
    switch (step.kind) {
      case CONSTANTS.STEP.work: {
        const exec = (step as WorkStepJson).execution as ExecutionJson | undefined;
        if (exec) executions.push(exec);
        break;
      }
      case CONSTANTS.STEP.branch: {
        const branch = step as BranchStepJson;
        for (const cw of branch.cases ?? []) {
          const whenExec = cw?.when?.execution as ExecutionJson | undefined;
          const whatExec = cw?.what?.execution as ExecutionJson | undefined;
          if (whenExec) executions.push(whenExec);
          if (whatExec) executions.push(whatExec);
        }
        break;
      }
      case CONSTANTS.STEP.while:
      case CONSTANTS.STEP.for: {
        const wrapper = (step as WhileStepJson).case || (step as ForStepJson).case;
        const whenExec = wrapper?.when?.execution as ExecutionJson | undefined;
        const whatExec = wrapper?.what?.execution as ExecutionJson | undefined;
        if (whenExec) executions.push(whenExec);
        if (whatExec) executions.push(whatExec);
        break;
      }
      default: break;
    }
  }
  return executions;
}

// Collect all jobIds referenced by executions
export function collectJobIds(spec: WorkflowSpecJson | null): ResourceIdJson[] {
  const executions = collectExecutions(spec);
  const ids = new Set<ResourceIdJson>();
  for (const exec of executions) {
    if (exec?.jobId) ids.add(exec.jobId as ResourceIdJson);
  }
  return Array.from(ids);
}

// Build a map of executionId -> execution for quick lookup
export function buildExecutionMap(spec: WorkflowSpecJson | null): Map<ExecutionIdJson, ExecutionJson> {
  const map = new Map<ExecutionIdJson, ExecutionJson>();
  for (const exec of collectExecutions(spec)) {
    if (exec?.id) map.set(exec.id as ExecutionIdJson, exec);
  }
  return map;
}
