import { useEffect } from 'react';
import type { WorkflowSpecJson, ExecutionJson, ResourceMapJson } from '@toolproof-npm/schema';
import type { JobJsonWithId } from '@/builders/workflow/_lib/types';
import { getUnboundInputs, getUnboundOutputs, bindOutputs, ensureExecution } from '@/builders/workflow/_lib/utils';

interface Params {
    workflowSpec: WorkflowSpecJson | null;
    activeExecution: ExecutionJson | null;
    jobDataMap: Map<string, JobJsonWithId>;
    setWorkflowSpec: React.Dispatch<React.SetStateAction<WorkflowSpecJson | null>>;
}

// Automatically bind outputs for an execution once all inputs are bound.
export function useAutoBindOutputs({ workflowSpec, activeExecution, jobDataMap, setWorkflowSpec }: Params) {
    useEffect(() => {
        if (!workflowSpec) return;
        if (!activeExecution) return;
        // Ensure we always pass a concrete ResourceMapJson (empty object fallback)
        const resourceMap = (workflowSpec.resourceMaps?.[0] ?? {}) as ResourceMapJson;
        const unboundInputs = getUnboundInputs(activeExecution, resourceMap);
        if (unboundInputs.length !== 0) return;

        const unboundOutputs = getUnboundOutputs(activeExecution, resourceMap);
        if (unboundOutputs.length === 0) return; // Already bound

        const job = jobDataMap.get(activeExecution.jobId);
        if (!job) return;

        const boundOutputs = bindOutputs(activeExecution, job);

        setWorkflowSpec(prev => {
            if (!prev) return prev;
            const maps = [...prev.resourceMaps];
            const resMap = { ...(maps[0] || {}) } as ResourceMapJson;
            const execBucket = ensureExecution(resMap, activeExecution.id);
            const newBucket = boundOutputs[activeExecution.id] || {};
            Object.assign(execBucket, newBucket);
            maps[0] = resMap;
            return { ...prev, resourceMaps: maps };
        });
    }, [workflowSpec, activeExecution, jobDataMap, setWorkflowSpec]);
}
