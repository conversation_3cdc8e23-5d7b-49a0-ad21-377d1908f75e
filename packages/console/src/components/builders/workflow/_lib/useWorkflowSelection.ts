import { useMemo } from 'react';
import type { Work<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Execution<PERSON>son, BranchStep<PERSON>son, WhileStep<PERSON>son, ForStepJson, ResourceIdJson } from '@toolproof-npm/schema';
import type { SelectedIndex, JobJsonWithId } from '@/builders/workflow/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';

interface SelectionResult {
  activeStep: StepJson | null;
  activeExecution: ExecutionJson | null;
  activeJob: JobJsonWithId | null;
}

export function useWorkflowSelection(
  workflowSpec: WorkflowSpecJson | null,
  selectedIndex: SelectedIndex | null,
  jobDataMap: Map<ResourceIdJson, JobJsonWithId>
): SelectionResult {
  return useMemo(() => {
    if (!workflowSpec || selectedIndex == null) {
      return { activeStep: null, activeExecution: null, activeJob: null };
    }
    const step = workflowSpec.workflow.steps[selectedIndex.stepIndex] || null;
    if (!step) {
      return { activeStep: null, activeExecution: null, activeJob: null };
    }

    let execution: ExecutionJson | null = null;
    switch (step.kind) {
      case CONSTANTS.STEP.work: {
        execution = step.execution as ExecutionJson;
        break;
      }
      case CONSTANTS.STEP.branch: {
        const cases = (step as BranchStepJson).cases ?? [];
        const rawIdx = selectedIndex.caseIndex ?? 0;
        const caseIdx = Math.max(0, Math.min(rawIdx, Math.max(cases.length - 1, 0)));
        execution = (cases?.[caseIdx]?.what.execution as ExecutionJson) ?? null;
        break;
      }
      case CONSTANTS.STEP.while:
      case CONSTANTS.STEP.for: {
        const wrapper = (step as WhileStepJson).case || (step as ForStepJson).case;
        execution = (wrapper?.what.execution as ExecutionJson) ?? null;
        break;
      }
      default: {
        execution = null;
      }
    }

    const job = execution ? jobDataMap.get(execution.jobId) || null : null;
    return { activeStep: step, activeExecution: execution, activeJob: job };
  }, [workflowSpec, selectedIndex, jobDataMap]);
}
