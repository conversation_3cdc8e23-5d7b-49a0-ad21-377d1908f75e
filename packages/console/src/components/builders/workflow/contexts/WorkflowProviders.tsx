
import type { SelectionContextValue } from '@/builders/workflow/contexts/SelectionContext';
import type { RoleExecutionContextValue } from '@/builders/workflow/contexts/RoleExecutionContext';
import type { ResourceDataContextValue } from '@/builders/workflow/contexts/ResourceDataContext';
import type { BindingsContextValue } from '@/builders/workflow/contexts/BindingsContext';
import type { DragContextValue } from '@/builders/workflow/contexts/DragContext';

import { SelectionProvider } from '@/builders/workflow/contexts/SelectionContext';
import { RoleExecutionProvider } from '@/builders/workflow/contexts/RoleExecutionContext';
import { ResourceDataProvider } from '@/builders/workflow/contexts/ResourceDataContext';
import { BindingsProvider } from '@/builders/workflow/contexts/BindingsContext';
import { DragProvider } from '@/builders/workflow/contexts/DragContext';


interface WorkflowProvidersProps {
  selection: SelectionContextValue;
  roleExecution: RoleExecutionContextValue;
  resourceData: ResourceDataContextValue;
  bindings: BindingsContextValue;
  drag: DragContextValue;
  children: React.ReactNode;
}

export function WorkflowProviders({ selection, roleExecution, resourceData, bindings, drag, children }: WorkflowProvidersProps) {
  return (
    <SelectionProvider value={selection}>
      <RoleExecutionProvider value={roleExecution}>
        <ResourceDataProvider value={resourceData}>
          <BindingsProvider value={bindings}>
            <DragProvider value={drag}>{children}</DragProvider>
          </BindingsProvider>
        </ResourceDataProvider>
      </RoleExecutionProvider>
    </SelectionProvider>
  );
}

export default WorkflowProviders;