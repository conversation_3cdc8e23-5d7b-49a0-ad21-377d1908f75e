import React, { createContext, useContext } from 'react';
import type { ResourceMapJson } from '@toolproof-npm/schema';
import type { ResourceDataMap } from '@toolproof-npm/shared/types';

export interface ResourceDataContextValue {
  resourceDataMap: ResourceDataMap;
  resourceMap: ResourceMapJson;
}

const ResourceDataContext = createContext<ResourceDataContextValue | undefined>(undefined);

export const ResourceDataProvider: React.FC<React.PropsWithChildren<{ value: ResourceDataContextValue }>> = ({ value, children }) => (
  <ResourceDataContext.Provider value={value}>{children}</ResourceDataContext.Provider>
);

export function useResourceDataContext(): ResourceDataContextValue {
  const ctx = useContext(ResourceDataContext);
  if (!ctx) throw new Error('useResourceDataContext must be used within a ResourceDataProvider');
  return ctx;
}