import React, { createContext, useContext } from 'react';
import type { RoleId<PERSON><PERSON>, ResourceDataJson, ExecutionIdJson } from '@toolproof-npm/schema';

export interface BindingsContextValue {
  onBindInputPath: (roleId: RoleId<PERSON>son, resourceData: ResourceDataJson) => void;
  onBindInputPointer: (roleId: RoleIdJ<PERSON>, source: { executionId: ExecutionIdJson; roleId: RoleIdJson }) => void;
  onClearInputBinding: (roleId: RoleIdJson) => void;
}

const BindingsContext = createContext<BindingsContextValue | undefined>(undefined);

export const BindingsProvider: React.FC<React.PropsWithChildren<{ value: BindingsContextValue }>> = ({ value, children }) => (
  <BindingsContext.Provider value={value}>{children}</BindingsContext.Provider>
);

export function useBindingsContext(): BindingsContextValue {
  const ctx = useContext(BindingsContext);
  if (!ctx) throw new Error('useBindingsContext must be used within a BindingsProvider');
  return ctx;
}