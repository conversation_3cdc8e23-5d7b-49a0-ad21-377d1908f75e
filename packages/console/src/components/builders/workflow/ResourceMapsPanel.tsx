import type { ResourceDataJson, ExecutionId<PERSON>son, RoleIdJson, ResourcePotentialJson } from '@toolproof-npm/schema';
import type { DragSource } from '@/builders/workflow/_lib/types';
import CircleBadge from '@/builders/workflow/CircleBadge';
import { useSelectionContext } from '@/builders/workflow/contexts/SelectionContext';
import { useRoleExecutionContext } from '@/builders/workflow/contexts/RoleExecutionContext';
import { useResourceDataContext } from '@/builders/workflow/contexts/ResourceDataContext';
import { useDragContext } from '@/builders/workflow/contexts/DragContext';

interface ResourceMapsPanelProps { excludeStepIds?: string[] }

export default function ResourceMapsPanel({ excludeStepIds = [] }: ResourceMapsPanelProps) {
    const { activeExecution } = useSelectionContext();
    const { executionMap, roleMap } = useRoleExecutionContext();
    const { resourceMap } = useResourceDataContext();
    const { setDragSource } = useDragContext();
    const cancelDrag = () => setDragSource(null);
    const excludeSet = new Set<string>(excludeStepIds.filter(Boolean));

    // console.log('roles:', JSON.stringify(Object.fromEntries(roles), null, 2));
    // console.log('activeExecution:', JSON.stringify(activeExecution, null, 2));

    return (
        <div>
            {(() => {
                const execEntries = Object.entries(resourceMap || {});
                if (execEntries.length === 0) return null;
                const tiles: { executionId: ExecutionIdJson; roleId: RoleIdJson; r: ResourcePotentialJson | ResourceDataJson }[] = [];
                for (const [execId, roleMapObj] of execEntries) {
                    if (!roleMapObj) continue;
                    if (activeExecution?.id === execId) continue; // exclude active execution resources
                    for (const [roleId, r] of Object.entries(roleMapObj as Record<string, ResourcePotentialJson | ResourceDataJson>)) {
                        if (!r) continue;
                        tiles.push({ executionId: execId as ExecutionIdJson, roleId: roleId as RoleIdJson, r });
                    }
                }
                if (tiles.length === 0) return null;
                return (
                    <div className='bg-blue-100'>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Resources</div>
                        <div className="flex flex-wrap gap-3">
                            {tiles.map(({ executionId, roleId, r }) => {
                                const role = roleMap.get(roleId);
                                if (!role) {
                                    console.warn(`Role not found for roleId: ${roleId}`);
                                    return null; // Skip this resource if role is not found
                                }

                                let isInput: boolean | undefined = undefined;
                                const originExec = executionMap.get(executionId as ExecutionIdJson);
                                if (originExec) {
                                    const inIds = Object.keys(originExec.roleBindings.inputBindingMap ?? {});
                                    const outIds = Object.keys(originExec.roleBindings.outputBindingMap ?? {});
                                    if (inIds.includes(roleId)) {
                                        isInput = true;
                                    } else if (outIds.includes(roleId)) {
                                        isInput = false;
                                    }
                                }
                                // Fallback: if still undefined, assume output to avoid mis-binding
                                if (isInput === undefined) isInput = false;
                                const dragPayload: DragSource = { executionId, roleId };

                                // For realized resources, use identity as title
                                let titleText: string = role.name;
                                if (r.kind === 'realized') {
                                    const ed = (r as ResourceDataJson).extractedData;
                                    titleText = String(ed.identity);
                                }

                                return (
                                    <div key={`${executionId}__${roleId}`} className="flex flex-col items-center">
                                        <CircleBadge
                                            as="div"
                                            draggable
                                            onDragStart={(e) => {
                                                // set drag source for drop handlers
                                                setDragSource(dragPayload);
                                                // set minimal data for native drag-and-drop
                                                try { e.dataTransfer?.setData('text/plain', JSON.stringify(dragPayload)); } catch (e) { }
                                            }}
                                            onDragEnd={() => cancelDrag()}
                                            className={isInput ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}
                                            title={titleText}
                                        >
                                            <span className="text-xs font-semibold">{isInput ? 'IN' : 'OUT'}</span>
                                        </CircleBadge>
                                        <div
                                            className="text-[11px] text-gray-700 mt-1 truncate max-w-28"
                                            title={titleText}
                                        >
                                            {role.name}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                );
            })()}
        </div>
    )

}