import type { <PERSON><PERSON><PERSON>, Work<PERSON>tep<PERSON><PERSON>, ResourceIdJson } from '@toolproof-npm/schema';
import type { SelectedIndex, JobJsonWithId } from '@/builders/workflow/_lib/types'
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import WorkStepTile from './WorkStepTile';


interface StepPanelProps {
    steps: (StepJson)[];
    selectedIndex: SelectedIndex | null;
    jobMap: Map<ResourceIdJson, JobJsonWithId>; // ATTENTION: Can't we just pass activeJob as a prop?
    workStepMap: Map<string, WorkStepJson>; // ATTENTION: Can't we just pass activeExecution as a prop? Maybe not, because we need to find workSteps by id for all steps, not just the active one.
    onSelect: (index: SelectedIndex) => void;
    onAddBranchStepCase: (jobId: ResourceIdJson) => void;
}

export default function StepPanel({ steps, selectedIndex, jobMap, workStepMap, onSelect, onAddBranchStepCase }: StepPanelProps) {

    return (
        <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Steps</h2>
                <p className="text-xs text-gray-500">Click to focus a step.</p>
            </div>
            <div className="flex-1 overflow-y-auto p-2">
                {steps.length === 0 ? (
                    <div className="text-sm text-gray-500 px-2 py-3">No steps yet. Drag a job to add it.</div>
                ) : (
                    <ul className="space-y-1">
                        {steps.map((step, i) => {
                            const isSelectedStep = selectedIndex?.stepIndex === i;
                            switch (step.kind) {
                                case CONSTANTS.STEP.work: {
                                    const workStep = step as WorkStepJson;
                                    const job = jobMap.get((workStep.execution as any)?.jobId as ResourceIdJson);
                                    if (!job) return null;
                                    return (
                                        <li key={step.id}>
                                            <WorkStepTile
                                                job={job}
                                                workStep={workStep}
                                                isSelected={isSelectedStep}
                                                onClick={() => onSelect({ stepIndex: i, caseIndex: null })}
                                            />
                                        </li>
                                    );
                                }
                                case CONSTANTS.STEP.branch: {
                                    null
                                }
                                case CONSTANTS.STEP.while: {
                                    null
                                }
                                case CONSTANTS.STEP.for: {
                                    null
                                }
                                default:
                                    return null;
                            }
                        })}
                    </ul>
                )}
            </div>
        </div>
    );
}
