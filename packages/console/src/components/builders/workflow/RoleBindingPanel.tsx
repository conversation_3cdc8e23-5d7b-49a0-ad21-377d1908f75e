import type { <PERSON><PERSON>d<PERSON><PERSON>, ResourceId<PERSON>son, ResourceDataJson, ExecutionIdJson } from '@toolproof-npm/schema';
import type { Role } from '@toolproof-npm/shared/types';
import type { DragSource } from '@/builders/workflow/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { ensureExecution } from '@/builders/workflow/_lib/utils';
import CircleBadge from '@/builders/workflow/CircleBadge';
import { useSelectionContext } from '@/builders/workflow/contexts/SelectionContext';
import { useResourceDataContext } from '@/builders/workflow/contexts/ResourceDataContext';
import { useDragContext } from '@/builders/workflow/contexts/DragContext';
import { useBindingsContext } from '@/builders/workflow/contexts/BindingsContext';
import { useMemo, useRef, useEffect, useState } from 'react';


export default function RoleBindingPanel() {
    const { activeStep, activeExecution, activeJob, selectedIndex } = useSelectionContext();
    const { resourceDataMap, resourceMap } = useResourceDataContext();
    const { dragSource, setDragSource } = useDragContext();
    const { onBindInputPath, onBindInputPointer, onClearInputBinding } = useBindingsContext();
    const resourcesData: ResourceDataJson[] = Object.values(resourceDataMap).flat();

    const inputRoles = useMemo(() => {
        if (!activeJob) return [] as Role[];
        const inputsObj = activeJob.roles.inputMap ?? {};
        return Object.entries(inputsObj).map(([id, role]) => ({ id, ...role } as Role));
    }, [activeJob]);
    // console.log('inputRoles', JSON.stringify(inputRoles, null, 2));

    const outputRoles = useMemo(() => {
        if (!activeJob) return [] as Role[];
        const outputsObj = activeJob.roles.outputMap ?? {};
        return Object.entries(outputsObj).map(([id, role]) => ({ id, ...role } as Role));
    }, [activeJob]);

    const handleDropOnInput = (roleId: RoleIdJson, roleName: string, _e?: DragEvent | React.DragEvent<Element>) => {
        const source: DragSource | null = dragSource ?? null;
        console.log('handleDropOnInput', { roleId, roleName, source, selectedIndex });
        if (source != null) {
            const inputRole = inputRoles.find(r => r.id === roleId);
            const inputType = inputRole?.typeId;
            const draggedResource = resourceMap?.[source.executionId]?.[source.roleId] as ResourceDataJson | undefined;
            const draggedTypeId = draggedResource?.typeId;
            if (inputType && draggedTypeId && inputType !== draggedTypeId) {
                setDragSource(null);
                return; // types mismatch
            }
            onBindInputPointer(roleId as RoleIdJson, { executionId: source.executionId as ExecutionIdJson, roleId: source.roleId as RoleIdJson });
            setDragSource(null);
        }
    };

    const [pickerRole, setPickerRole] = useState<string | null>(null);
    // For WhileStep: which role id is designated as the iterating input
    const [iteratingRoleId, setIteratingRoleId] = useState<string | null>(null);

    // reset iterating selection when step changes
    useEffect(() => {
        setIteratingRoleId(null); // ATTENTION
    }, [activeStep?.id]);

    // Close picker when clicking anywhere in the panel outside the picker trigger/menu
    const panelBodyRef = useRef<HTMLDivElement | null>(null);
    useEffect(() => {
        const onDocClick = (e: MouseEvent) => {
            if (panelBodyRef.current && !panelBodyRef.current.contains(e.target as Node)) {
                setPickerRole(null);
            }
        };
        document.addEventListener('mousedown', onDocClick);
        return () => {
            document.removeEventListener('mousedown', onDocClick);
        };
    }, []);

    // console.log('activeJob', JSON.stringify(activeJob, null, 2));
    // console.log('activeExecution', JSON.stringify(activeExecution, null, 2));
    // Narrow required workflow entities after all hooks to satisfy TS and rules-of-hooks.
    if (!activeStep || !activeExecution || !activeJob) {
        return null;
    }
    const stepKind = activeStep.kind;

    return (
        <div className="h-full flex flex-col bg-red-50">
            <div className="flex-1 select-none" ref={panelBodyRef} onClick={() => setPickerRole(null)}>
                <div className="space-y-4 p-0">
                    <div className="text-sm text-gray-800 font-medium mb-2 px-0">Step: {activeStep.id || '(unknown)'}{stepKind ? ` (${stepKind})` : ''}</div>
                    {/* Current step inputs */}
                    <div>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Inputs</div>
                        <div className="flex flex-wrap gap-3">
                            {inputRoles.length === 0 ? (
                                <div className="text-sm text-gray-500">No inputs</div>
                            ) : (
                                inputRoles.map((input, i) => {
                                    const inputBindingId = activeExecution.roleBindings.inputBindingMap?.[input.id as RoleIdJson] as ResourceIdJson;
                                    const isBound = !!inputBindingId && !!resourceMap?.[activeExecution.id]?.[input.id as RoleIdJson];
                                    const circleCls = isBound
                                        ? 'bg-green-500 text-white border-green-600'
                                        : 'bg-white text-green-600 border-green-500 hover:bg-green-50';
                                    let val: string = '<UNBOUND>';
                                    if (isBound) {
                                        const inputEntry = (resourceMap?.[activeExecution.id]?.[input.id as RoleIdJson] as ResourceDataJson | undefined) ?? undefined;
                                        const semId = inputEntry?.extractedData?.identity;
                                        // Use nullish check so 0/'' are preserved, and coerce to string for display
                                        val = semId != null ? String(semId) : '<UNBOUND>';
                                    }

                                    return (
                                        <div key={`in-${input.name}-${i}`} className="flex flex-col items-center relative">
                                            <CircleBadge
                                                as="button"
                                                className={`border-2 ${circleCls} ring-2 ring-blue-200`}
                                                title={val}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setPickerRole((prev) => (prev === input.name ? null : input.name));
                                                }}
                                                onDragOver={(e) => {
                                                    if (!dragSource) return;
                                                    const dragged = resourceMap?.[dragSource.executionId]?.[dragSource.roleId] as ResourceDataJson | undefined;
                                                    if (dragged && dragged.typeId === input.typeId) e.preventDefault();
                                                }}
                                                onDrop={(e) => { e.preventDefault(); handleDropOnInput(input.id as RoleIdJson, input.name, e); setPickerRole(null); }}
                                                onContextMenu={(e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    if (isBound && inputBindingId) {
                                                        onClearInputBinding(input.id as RoleIdJson);
                                                    }
                                                }}
                                            >
                                                <span className="text-xs font-semibold">IN</span>
                                            </CircleBadge>
                                            <div className="text-[11px] text-gray-700 mt-1">{input.name}</div>

                                            {/* If this is a WhileStep or ForStep, render an iterate toggle for one input */}

                                            {(stepKind === CONSTANTS.STEP.while || stepKind === CONSTANTS.STEP.for) && (
                                                <div className="mt-2 text-[11px]">
                                                    <label className="inline-flex items-center gap-2">
                                                        <input
                                                            type="radio"
                                                            name="iteratingRole"
                                                            checked={iteratingRoleId === input.id}
                                                            onChange={() => setIteratingRoleId(input.id)}
                                                        />
                                                        <span>Iterating input</span>
                                                    </label>
                                                </div>
                                            )}

                                            {pickerRole === input.name && (
                                                <div className="absolute z-10 top-12 left-1/2 -translate-x-1/2 w-64 max-h-56 overflow-auto bg-white border border-gray-200 rounded shadow-lg" onClick={(e) => e.stopPropagation()}>
                                                    <div className="sticky top-0 bg-gray-50 text-[11px] text-gray-600 px-2 py-1">Choose a file</div>
                                                    <ul className="py-1">
                                                        {resourcesData.filter((resourceData) => resourceData.typeId === input.typeId).map((resourceData, i) => (
                                                            <li key={i}>
                                                                <button
                                                                    className="w-full text-left px-3 py-2 text-sm hover:bg-green-50"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        onBindInputPath(input.id as RoleIdJson, resourceData);
                                                                        setPickerRole(null);
                                                                    }}
                                                                >
                                                                    {String(resourceData.extractedData.identity)}
                                                                </button>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                    <div className="border-t text-[11px] text-gray-500 px-2 py-1">Right-click input to clear</div>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })
                            )}
                        </div>
                    </div>

                    {/* Current step outputs */}
                    <div>
                        <div className="text-xs uppercase tracking-wide text-gray-500 mb-2">Outputs</div>
                        <div className="flex flex-wrap gap-3">
                            {outputRoles.length === 0 ? (
                                <div className="text-sm text-gray-500">No outputs</div>
                            ) : (
                                outputRoles.map((output, idx) => {
                                    const outputBindingId = activeExecution.roleBindings.outputBindingMap?.[output.id] as ResourceIdJson;
                                    const isBound = !!outputBindingId && !!resourceMap?.[activeExecution.id]?.[output.id as RoleIdJson];
                                    const circleCls = isBound
                                        ? 'bg-red-500 text-white border-red-600'
                                        : 'bg-white text-red-600 border-red-500 hover:bg-red-50';
                                    let val: string = '<UNBOUND>';
                                    if (isBound && outputBindingId) {
                                        const outputEntry = (resourceMap?.[activeExecution.id]?.[output.id as RoleIdJson] as ResourceDataJson | undefined) ?? undefined;
                                        const semId = outputEntry?.extractedData?.identity;
                                        // Use nullish check so 0/'' are preserved, and coerce to string for display
                                        val = semId != null ? String(semId) : '<UNBOUND>';
                                    }

                                    return (
                                        <div key={`out-${output.name}-${idx}`} className="flex flex-col items-center">
                                            <CircleBadge
                                                as={(stepKind !== CONSTANTS.STEP.work) ? 'button' : 'div'}
                                                draggable={(stepKind !== CONSTANTS.STEP.work)}
                                                onDragStart={() => {
                                                    if (!outputBindingId) return;
                                                    const dragPayload: DragSource = { executionId: activeExecution.id as ExecutionIdJson, roleId: output.id as RoleIdJson };
                                                    setDragSource(dragPayload);
                                                }}
                                                onDragEnd={() => setDragSource(null)}
                                                className={`border-2 ${circleCls} ring-2 ring-blue-200`}
                                                title={val}
                                            >
                                                <span className="text-xs font-semibold">OUT</span>
                                            </CircleBadge>
                                            <div className="text-[11px] text-gray-700 mt-1">{output.name}</div>
                                        </div>
                                    )
                                })
                            )}
                        </div>
                    </div>

                    <div className="text-xs text-gray-500">
                        Tip: Drag a red output onto a green input to bind it; or click an input to choose from preset calculator json files. Right-click an input to clear.
                    </div>
                </div>
            </div>
        </div>
    );
}
