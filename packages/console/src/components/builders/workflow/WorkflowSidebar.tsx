import React from 'react';
import type { ResourceId<PERSON><PERSON>, StepJson, WorkStepJson } from '@toolproof-npm/schema';
import type { JobJsonWithId, SelectedIndex } from '@/builders/workflow/_lib/types';
import StepPanel from '@/builders/workflow/stepPanel/StepPanel';
import ResourceMapsPanel from '@/builders/workflow/ResourceMapsPanel';
import RoleBindingPanel from '@/builders/workflow/RoleBindingPanel';
import { useSelectionContext } from '@/builders/workflow/contexts/SelectionContext';

interface WorkflowSidebarProps {
  steps: StepJson[];
  workStepMap: Map<string, WorkStepJson>;
  jobDataMap: Map<ResourceIdJson, JobJsonWithId>;
  selectedIndex: SelectedIndex | null;
  onSelectStep: (index: SelectedIndex) => void;
  onAddBranchStepCase: (jobId: ResourceIdJson) => void;
}

export default function WorkflowSidebar(props: WorkflowSidebarProps) {
  const {
    steps,
    workStepMap,
    jobDataMap,
    selectedIndex,
    onSelectStep,
    onAddBranchStepCase,
  } = props;
  const { activeStep, activeExecution, activeJob, excludeCaseStepIds } = useSelectionContext();

  return (
    <>
      {/* Step list */}
      <div className="w-80 bg-white border-l border-gray-200">
        <StepPanel
          steps={steps}
          workStepMap={workStepMap}
          jobMap={jobDataMap}
          selectedIndex={selectedIndex}
          onSelect={onSelectStep}
          onAddBranchStepCase={onAddBranchStepCase}
        />
      </div>

      {/* Resource bindings */}
      {(activeStep && activeExecution && activeJob && selectedIndex) ? (
        <div className="w-96 bg-white border-l border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Resources</h2>
            <p className="text-xs text-gray-500">Drag outputs to inputs or pick files.</p>
          </div>
          <div className="flex-1 overflow-auto p-4 space-y-4">
            <ResourceMapsPanel excludeStepIds={excludeCaseStepIds} />
            <RoleBindingPanel />
          </div>
        </div>
      ) : null}
    </>
  );
}
