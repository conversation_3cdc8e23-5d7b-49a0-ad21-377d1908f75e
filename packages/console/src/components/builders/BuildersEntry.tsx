'use client';

import type { ArchetypeConst } from '@toolproof-npm/shared/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import FormatBuilder from '@/builders/format/FormatBuilder';
import TypeBuilder from '@/builders/type/TypeBuilder';
import JobBuilder from '@/builders/job/JobBuilder';
import ResourceBuilder from './resource/ResourceBuilder';
import WorkflowBuilder from '@/builders/workflow/WorkflowBuilder';
import { useState } from 'react';
import type { CosmosWorldData } from '@/explorer/worlds/cosmos/_lib/types';
import type { ArchetypeMetaMap } from '@toolproof-npm/shared/types';
import type { FormatMeta<PERSON>son, TypeDataJson, TypeMetaJson } from '@toolproof-npm/schema';

type BuilderKey = ArchetypeConst | typeof CONSTANTS.RESOURCES.resources | typeof CONSTANTS.RESOURCES.jobs | typeof CONSTANTS.WORKFLOW.workflow;

const builders: { key: BuilderKey; label: string }[] = [
    { key: CONSTANTS.ARCHETYPES.formats, label: 'Format' },
    { key: CONSTANTS.ARCHETYPES.types, label: 'Type' },
    { key: CONSTANTS.RESOURCES.resources, label: 'Resource' },
    { key: CONSTANTS.RESOURCES.jobs, label: 'Job' },
    { key: CONSTANTS.WORKFLOW.workflow, label: 'Workflow' },
];

interface BuildersEntryProps {
    cosmosWorldData: CosmosWorldData;
    typeMetaMap: ArchetypeMetaMap<TypeMetaJson>;
    typeDataMap: { members: TypeDataJson[]; specials: TypeDataJson[] };
    formatMetaMap: ArchetypeMetaMap<FormatMetaJson>;
}

export default function BuildersEntry({ cosmosWorldData, typeMetaMap, typeDataMap, formatMetaMap }: BuildersEntryProps) {
    const [active, setActive] = useState<BuilderKey>(CONSTANTS.ARCHETYPES.formats);

    return (
        <div className="p-4">
            <h1 className="text-2xl font-semibold mb-4">Builders</h1>

            {/* Top controls: Archetypes and Resources on the left; Workflow on the right */}
            <div className="flex items-center justify-between flex-wrap gap-3 mb-6">
                {/* Left: Archetypes and Resources groups */}
                <div className="flex items-center gap-4 flex-wrap">
                    {/* Archetypes */}
                    <div className="flex items-center gap-2 flex-wrap">
                        <span className="text-xs font-medium uppercase tracking-wide text-gray-500 mr-1">Archetypes</span>
                        {([CONSTANTS.ARCHETYPES.formats, CONSTANTS.ARCHETYPES.types] as BuilderKey[]).map((key) => {
                            const label = builders.find((b) => b.key === key)?.label ?? String(key);
                            return (
                                <button
                                    key={key}
                                    onClick={() => setActive(key)}
                                    className={`px-3 py-1.5 rounded border text-sm transition-colors ${active === key
                                        ? 'bg-blue-600 text-white border-blue-600'
                                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                        }`}
                                    aria-pressed={active === key}
                                >
                                    {label}
                                </button>
                            );
                        })}
                    </div>

                    {/* Resources */}
                    <div className="flex items-center gap-2 flex-wrap">
                        <span className="text-xs font-medium uppercase tracking-wide text-gray-500 mr-1">Resources</span>
                        {([CONSTANTS.RESOURCES.resources, CONSTANTS.RESOURCES.jobs] as BuilderKey[]).map((key) => {
                            const label = builders.find((b) => b.key === key)?.label ?? String(key);
                            return (
                                <button
                                    key={key}
                                    onClick={() => setActive(key)}
                                    className={`px-3 py-1.5 rounded border text-sm transition-colors ${active === key
                                        ? 'bg-blue-600 text-white border-blue-600'
                                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                        }`}
                                    aria-pressed={active === key}
                                >
                                    {label}
                                </button>
                            );
                        })}
                    </div>
                </div>

                {/* Right: Workflow button */}
                <div className="flex items-center">
                    <button
                        onClick={() => setActive(CONSTANTS.WORKFLOW.workflow)}
                        className={`px-3 py-1.5 rounded border text-sm transition-colors ${active === CONSTANTS.WORKFLOW.workflow
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                            }`}
                        aria-pressed={active === CONSTANTS.WORKFLOW.workflow}
                    >
                        Workflow
                    </button>
                </div>
            </div>

            <div className="bg-white rounded border border-gray-200 p-4">
                {active === CONSTANTS.ARCHETYPES.formats && (
                    <FormatBuilder />
                )}
                {active === CONSTANTS.ARCHETYPES.types && (
                    <TypeBuilder formatMetaMap={formatMetaMap} />
                )}
                {active === CONSTANTS.RESOURCES.jobs && (
                    <JobBuilder typeMetaMap={typeMetaMap} />
                )}
                {active === CONSTANTS.RESOURCES.resources && (
                    <ResourceBuilder typeMetaMap={typeMetaMap} typeDataMap={typeDataMap} />
                )}
                {active === CONSTANTS.WORKFLOW.workflow && (
                    <WorkflowBuilder cosmosWorldData={cosmosWorldData} />
                )}
            </div>
        </div>
    );
}
