'use client';

import type { <PERSON>Id<PERSON>son, FormatIdJson, FormatMetaJson, ExtractionSchemaJson, TypeDataJson } from '@toolproof-npm/schema';
import type { UploadResult } from '@/builders/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateArchetype, validateResource } from '@toolproof-npm/validation';
import { getUiContext } from '@/builders/_lib/utils';
import { DropDown } from '@/builders/_lib/DropDown';
import { JsonEditor } from '@/builders/_lib/JsonEditor';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import { LabeledInput } from '@/builders/_lib/LabeledInput';
import { LabeledCheckbox } from '@/builders/_lib/LabeledCheckbox';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/builders/_lib/SaveControls';
import { getNewId, uploadArchetype } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useMemo, useState, useEffect } from 'react';
import type { ArchetypeMetaMap } from '@toolproof-npm/shared/types';


const defaultExtractionSchema: ExtractionSchemaJson = {
    $schema: 'https://json-schema.org/draft/2020-12/schema',
    type: 'object',
    properties: {
        identity: { type: 'integer' },
    },
    required: ['identity'],
    additionalProperties: false,
};

const defaultType: TypeDataJson = {
    id: '' as TypeIdJson,
    name: 'Integer',
    description: 'dummy-description',
    formatId: '' as FormatIdJson,
    extractionSchema: defaultExtractionSchema,
};

const defaultSampleResource = { identity: 0 };

interface TypeBuilderProps {
    formatMetaMap: ArchetypeMetaMap<FormatMetaJson>;
}

export default function TypeBuilder({ formatMetaMap: injectedFormatMetaMap }: TypeBuilderProps) {
    const [id, setId] = useState<string>(defaultType.id);
    const [name, setName] = useState(defaultType.name);
    const [description, setDescription] = useState(defaultType.description);
    const [isSpecial, setIsSpecial] = useState<boolean>(false);
    const effectiveFormatMetaMap = injectedFormatMetaMap;
    const formatsMeta = effectiveFormatMetaMap.specials.filter((fmt) => fmt.id !== CONSTANTS.SPECIALS.FORMAT_ApplicationJob || true); // DOC: Exclude ApplicationJob format from selection
    const [selectedFormatId, setSelectedFormatId] = useState<string>(defaultType.formatId as string);
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);

    const [extractionSchema, setExtractionSchema] = useState<ExtractionSchemaJson>(defaultType.extractionSchema);
    const [extractionSchemaText, setExtractionSchemaText] = useState<string>(
        JSON.stringify(defaultType.extractionSchema, null, 2)
    );
    const [extractionSchemaParseError, setExtractionSchemaParseError] = useState<string | null>(null);

    const [sampleResource, setSampleResource] = useState<unknown>(defaultSampleResource);
    const [sampleResourceText, setSampleResourceText] = useState<string>(JSON.stringify(defaultSampleResource, null, 2));
    const [sampleResourceParseError, setSampleResourceParseError] = useState<string | null>(null);

    // DOC: Let an instance of the archetype be defined by state variables
    const type: TypeDataJson = useMemo(
        () => ({
            id: id as TypeIdJson,
            name,
            description,
            formatId: selectedFormatId as FormatIdJson,
            extractionSchema,
        }),
        [id, name, description, selectedFormatId, extractionSchema]
    );

    // DOC: Fetch a new id for the archetype
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!id) {
                const newId = await getNewId(CONSTANTS.ARCHETYPES.types);
                setId(newId);
            }
        };
        asyncWrapper();
    }, [id]);

    // DOC: When dependent archetypes arrive, auto-select the first
    useEffect(() => {
        if (!selectedFormatId && formatsMeta.length) {
            setSelectedFormatId(formatsMeta[0].id);
        }
    }, [formatsMeta, selectedFormatId]);

    // DOC: Display errors from loading dependent archetypes
    // No external loading/error when maps are injected and gated by parent

    // DOC: Validate the archetype locally
    const isValidLocal = useMemo(() => validateLocally(type), [type]);

    const uiContext = getUiContext();

    // DOC: Validate the archetype formally against its schema
    const { isValid: isValidFormal, errors } = validateArchetype(CONSTANTS.SCHEMA.TypeData, type, uiContext);

    // console.log('-----------');
    // console.log('TypeBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal && !extractionSchemaParseError && isValidFormal;

    // DOC: Validate sampleResource against extractionSchema
    const { isValid: isValidSample, errors: errorsSample } = validateResource(extractionSchema, sampleResource);

    // DOC: Update extractionSchema state on text change, with parse error handling
    const handleExtractionSchemaChange = (text: string) => {
        setExtractionSchemaText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setExtractionSchemaParseError('extractionSchema must be a JSON Schema object.');
                return;
            }
            setExtractionSchema(parsed as ExtractionSchemaJson);
            setExtractionSchemaParseError(null);
        } catch (e) {
            setExtractionSchemaParseError((e as Error).message);
        }
    };

    // DOC: Update sampleResource state on text change, with parse error handling
    const handleSampleResourceChange = (text: string) => {
        setSampleResourceText(text);
        try {
            const parsed = JSON.parse(text);
            if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
                setSampleResourceParseError('sampleResource must be a JSON object.');
                return;
            }
            setSampleResource(parsed);
            setSampleResourceParseError(null);
        } catch (e) {
            setSampleResourceParseError((e as Error).message);
        }
    };

    // DOC: Upload the archetype upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        // DOC: 'extractionSchema' is excluded from the archetype metadata saved to Firestore
        const res = (await uploadArchetype(CONSTANTS.ARCHETYPES.types, isSpecial ? CONSTANTS.STORAGE.FILTER.specials : CONSTANTS.STORAGE.FILTER.members, type, ['extractionSchema'], {})) as UploadResult;
        if (res?.ok) {
            const { docId } = res;
            setSaveStatus(`Saved. Firestore doc: ${docId}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='type-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {isSpecial ? (
                        <LabeledInput label='ID' value={id} onChange={setId} placeholder='Custom ID' />
                    ) : (
                        <>
                            {/* DOC: 'id' is generated server-side */}
                            <ReadOnlyIdField value={id} />
                        </>
                    )}

                    <DropDown
                        items={formatsMeta}
                        value={selectedFormatId}
                        label='Format'
                        // loading={formatMetaMapLoading}
                        onChange={(newFormatId) => {
                            setSelectedFormatId(newFormatId);
                        }}
                    />
                    <LabeledInput
                        label='Name'
                        value={name}
                        onChange={setName}
                        placeholder={defaultType.name}
                        error={isValidLocal.errors.name}
                    />
                    <LabeledInput
                        label='Description'
                        value={description}
                        onChange={setDescription}
                        placeholder={defaultType.description}
                        error={isValidLocal.errors.description}
                    />
                    <LabeledCheckbox label='isSpecial' checked={isSpecial} onChange={setIsSpecial} />
                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                        <JsonEditor
                            legend='extractionSchema (JSON Schema draft 2020-12)'
                            valueText={extractionSchemaText}
                            onChangeText={handleExtractionSchemaChange}
                            parseError={extractionSchemaParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview {name}</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {loadingPreview
                                    ? 'Loading…'
                                    : JSON.stringify(
                                        type,
                                        null,
                                        2
                                    )}
                            </pre>
                            <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
                        </section>
                    </div>

                    <div>
                        <JsonEditor
                            legend='sampleResource (validated against extractionSchema)'
                            valueText={sampleResourceText}
                            onChangeText={handleSampleResourceChange}
                            parseError={sampleResourceParseError}
                            heightClass='h-64'
                        />

                        <section className='mt-6'>
                            <h3 className='font-semibold mb-2'>Preview SampleResource</h3>
                            <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                                {JSON.stringify(sampleResource, null, 2)}
                            </pre>
                            {errorsSample?.length ? (
                                <ValidationErrors errors={errorsSample} />
                            ) : (
                                <div className='text-sm text-green-700 mt-2'>
                                    SampleResource is valid against current extractionSchema.
                                </div>
                            )}
                        </section>
                    </div>
                </div>
            </form>

            <SaveControls
                formId='type-form'
                buttonText='Save Type'
                disabled={!isValid || !id}
                isValid={isValid}
                invalidMessage='Fix errors before saving.'
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}

function validateLocally(
    type: TypeDataJson
): { valid: boolean; errors: Record<string, string | undefined> } {
    const errors: Record<string, string | undefined> = {};

    // id is server-generated; no client-side validation
    if (!type.name.trim()) errors.name = 'name is required';
    // if (!rt.description?.trim()) errors.description = 'Description is required';
    if (!type.formatId) errors.formatId = 'formatId is required';
    if (!type.extractionSchema || typeof type.extractionSchema !== 'object' || Array.isArray(type.extractionSchema)) {
        errors.extractionSchema = 'extractionSchema must be a JSON Schema object';
    }

    return { valid: Object.keys(errors).length === 0, errors };
}
