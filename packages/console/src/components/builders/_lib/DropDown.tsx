import { ArchetypeMeta as ArchetypeMetaJson } from '@toolproof-npm/shared/types';

export function DropDown<T extends ArchetypeMetaJson>(props: {
  items: T[];
  value: string;
  label: string
  onChange: (id: string) => void;
  loading?: boolean;
}) {
  const { items, value, label, onChange, loading } = props;
  return (
    <div>
      <label className='block text-sm font-medium mb-1'>{label}</label>
      <select
        className='w-full rounded border border-gray-300 px-3 py-2'
        value={value}
        onChange={(e) => onChange(e.target.value)}
      >
        {loading ? (
          <option>Loading…</option>
        ) : (
          items.map((item) => (
            <option key={item.id} value={item.id}>
              {String(item.name ?? item.id)}
            </option>
          ))
        )}
      </select>
    </div>
  );
}
