'use client';

import type { FormatId<PERSON>son, FormatDataJson } from '@toolproof-npm/schema';
import type { UploadResult } from '@/builders/_lib/types';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { validateArchetype } from '@toolproof-npm/validation';
import { getUiContext } from '@/builders/_lib/utils';
import { LabeledInput } from '@/builders/_lib/LabeledInput';
import { LabeledCheckbox } from '@/builders/_lib/LabeledCheckbox';
import ReadOnlyIdField from '@/builders/_lib/ReadOnlyIdField';
import SaveControls from '@/builders/_lib/SaveControls';
import { ValidationErrors } from '@/builders/_lib/ValidationErrors';
import { getNewId, uploadArchetype } from '@/_lib/server/firebaseAdminHelpers';
import type { ErrorObject } from 'ajv';
import { useState, useEffect, useMemo } from 'react';


export default function FormatBuilder() {
    const [id, setId] = useState<string>('');
    const [name, setName] = useState<string>('');
    const [description, setDescription] = useState<string>('dummy-description');
    const [isSpecial, setIsSpecial] = useState<boolean>(false);
    const [loadingPreview, setLoadingPreview] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [saveStatus, setSaveStatus] = useState<string | null>(null);

    // DOC: Let an instance of the archetype be defined by state variables
    const format: FormatDataJson = useMemo(
        () => {
            return {
                id: id as FormatIdJson,
                name,
                description,
            };
        },
        [id, name, description]
    );

    // DOC: Fetch a new id for the archetype
    useEffect(() => {
        const asyncWrapper = async () => {
            if (!id) {
                const newId = await getNewId(CONSTANTS.ARCHETYPES.formats);
                setId(newId);
            }
        };
        asyncWrapper();
    }, [id]);

    // DOC: Validate the archetype locally
    const isValidLocal = Boolean(id.trim() && name.trim());

    const uiContext = getUiContext();

    // DOC: Validate the archetype formally against its schema
    const { isValid: isValidFormal, errors } = validateArchetype(CONSTANTS.SCHEMA.FormatData, format, uiContext);

    // console.log('-----------');
    // console.log('FormatBuilder - uiContext:', JSON.stringify(uiContext, null, 2));

    const isValid = isValidLocal && isValidFormal;

    // DOC: Upload the archetype upon form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!isValid) return;
        const res = (await uploadArchetype(CONSTANTS.ARCHETYPES.formats, isSpecial ? CONSTANTS.STORAGE.FILTER.specials : CONSTANTS.STORAGE.FILTER.members, format, [], {})) as UploadResult;
        if (res?.ok) {
            const { docId } = res;
            setSaveStatus(`Saved. Firestore doc: ${docId}`);
        } else {
            setSaveStatus(`Save failed: ${res?.error ?? 'Unknown error'}`);
        }
    };

    return (
        <div>
            <form id='format-form' onSubmit={handleSubmit} className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {isSpecial ? (
                        <LabeledInput label='ID' value={id} onChange={setId} placeholder='Custom ID' />
                    ) : (
                        <>
                            {/* DOC: 'id' is generated server-side */}
                            <ReadOnlyIdField value={id} />
                        </>
                    )}

                    <LabeledInput label='Name' value={name} onChange={setName} placeholder='Format name' />

                    <LabeledInput label='Description' value={description} onChange={setDescription} placeholder='What this format is for' />

                    <LabeledCheckbox label='isSpecial' checked={isSpecial} onChange={setIsSpecial} />
                </div>

                <div>
                    <h3 className='font-semibold mb-2'>Preview {name}</h3>
                    <pre className='bg-gray-100 p-3 rounded overflow-auto text-sm h-64'>
                        {loadingPreview
                            ? 'Loading…'
                            : JSON.stringify(
                                format,
                                null,
                                2
                            )}
                    </pre>
                    <ValidationErrors errors={errors as ErrorObject[] | null | undefined} />
                </div>
            </form>

            <SaveControls
                formId='format-form'
                buttonText='Save Format'
                disabled={!isValid}
                isValid={isValid}
                invalidMessage='Fill all fields before saving.'
                error={error}
                saveStatus={saveStatus}
                className='mt-4'
            />
        </div>
    );
}
