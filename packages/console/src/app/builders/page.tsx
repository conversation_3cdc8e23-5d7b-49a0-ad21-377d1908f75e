"use client";
import BuildersEntry from '@/builders/BuildersEntry';
import CosmosDataProvider from '@/components/explorer/contexts/CosmosDataProvider';


export default function Page() {
    return (
        <CosmosDataProvider>
            {({ cosmosWorldData, typeMetaMap, typeDataMap, formatMetaMap, loading, error }) => (
                loading ? (
                    <div className="flex h-[100svh] items-center justify-center text-sm text-gray-400">
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-transparent" />
                        Loading Cosmos data…
                    </div>
                ) : error ? (
                    <div className="flex h-[100svh] items-center justify-center px-4 text-sm text-red-500">
                        Failed to load Cosmos data: {String((error as any)?.message ?? error)}
                    </div>
                ) : (
                    <BuildersEntry
                        cosmosWorldData={cosmosWorldData}
                        typeMetaMap={typeMetaMap}
                        typeDataMap={typeDataMap}
                        formatMetaMap={formatMetaMap}
                    />
                )
            )}
        </CosmosDataProvider>
    );
}
