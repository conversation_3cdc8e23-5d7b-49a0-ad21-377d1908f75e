{"name": "@toolproof/engine", "version": "1.0.0", "description": "LangGraph-based workflow execution engine for ToolProof", "keywords": ["toolproof", "engine", "langgraph", "workflow", "execution"], "author": "ToolProof Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ToolProof/core.git", "directory": "packages/engine"}, "homepage": "https://github.com/ToolProof/core#readme", "bugs": {"url": "https://github.com/ToolProof/core/issues"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "files": ["dist", "README.md"], "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "packageManager": "pnpm@10.15.0", "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/index.js", "start:GraphRunWorkflow": "cross-env NODE_ENV=GraphRunWorkflow node --loader ts-node/esm src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@babel/generator": "^7.28.5", "@babel/parser": "^7.28.5", "@babel/traverse": "^7.28.5", "@google-cloud/storage": "^7.17.3", "@langchain/core": "^0.3.79", "@langchain/langgraph": "^0.3.12", "@langchain/langgraph-sdk": "^0.0.74", "@langchain/openai": "^0.4.9", "@toolproof-npm/schema": "^0.1.20", "@toolproof-npm/shared": "^0.1.18", "@types/uuid": "^10.0.0", "axios": "^1.13.2", "dotenv": "^16.6.1", "firebase-admin": "^13.6.0", "openai": "^4.104.0", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@types/babel__generator": "^7.27.0", "@types/babel__traverse": "^7.28.0", "@types/ws": "^8.18.1", "cross-env": "^10.1.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.9.3"}}