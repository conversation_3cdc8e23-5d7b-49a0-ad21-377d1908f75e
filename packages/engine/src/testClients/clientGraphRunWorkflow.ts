import dotenv from 'dotenv';
dotenv.config();
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import { WorkflowIdJson, WorkflowSpecIdJson, WorkflowSpecJson } from '@toolproof-npm/schema';
import { Client } from '@langchain/langgraph-sdk';
import { RemoteGraph } from '@langchain/langgraph/remote';
import { HumanMessage } from '@langchain/core/messages';


const urlLocal = `http://localhost:2024`;
const urlRemote = `https://ht-silver-silence-54-960b977fef625b03bcb03971e056e70d.us.langgraph.app`;
const url = urlLocal; //process.env.URL || urlLocal;
const graphId = CONSTANTS.ENGINE.GraphRunWorkflow;
const client = new Client({
    apiUrl: url,
});
const remoteGraph = new RemoteGraph({ graphId, url });

export async function runRemoteGraph() {
    try {
        // Create a thread (or use an existing thread instead)
        const thread = await client.threads.create();
        // console.log('thread :', thread);
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 1800000); // 30 minutes
        // console.log('timeout :', timeout);

        const workflowSpec: WorkflowSpecJson = {
            id: 'dummy_workflowSpec' as WorkflowSpecIdJson,
            workflow: {
                id: 'dummy_workflow' as WorkflowIdJson,
                steps: []
            },
            resourceMaps: [],
        };

        try {
            // console.log('Invoking the graph')
            const result = await remoteGraph.invoke({
                messages: [new HumanMessage('Graph is invoked')],
                dryModeManager: {
                    dryRunMode: true,
                    delay: 1000,
                    drySocketMode: true,
                },
                workflowSpec
            }, {
                configurable: { thread_id: thread.thread_id },
                signal: controller.signal,
            });

            // console.log('threadId:', thread.thread_id);
            console.log('result:', JSON.stringify(result.messages, null, 2));

            return result;

        } finally {
            clearTimeout(timeout);
            if (!controller.signal.aborted) {
                controller.abort();
            }
        }

    } catch (error) {
        console.error('Error invoking graph:', error);
    }

}
