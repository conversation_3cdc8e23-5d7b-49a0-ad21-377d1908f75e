import type { <PERSON>S<PERSON><PERSON>son, ResourceMapJson, JsonValue<PERSON>son, ResourcePotentialInputJson, ResourcePotentialOutputJson, ResourceDataJson, ResourceIdJson, RoleIdJson, ResourceSocketJson } from '@toolproof-npm/schema';
import * as CONSTANTS_LOCAL from '../constants.js';
import { fetchData } from '../_lib/ioHelper.js';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';
import axios from 'axios';


type ResolveResult =
    | { status: 'realized'; entry: ResourceDataJson; path: ResourceSocketJson[] }
    | { status: 'blocked-output'; entry: ResourcePotentialOutputJson; path: ResourceSocketJson[] }
    | { status: 'unresolved'; reason: 'missing-entry' | 'cycle' | 'depth-exceeded'; path: ResourceSocketJson[] };

function resolveResourceChain(resourceMap: ResourceMapJson, start: ResourceSocketJson, maxDepth = 50): ResolveResult {
    const visited = new Set<string>();
    const path: ResourceSocketJson[] = [];
    let current: ResourceSocketJson = start;
    for (let depth = 0; depth <= maxDepth; depth++) {
        path.push(current);
        const key = `${current.executionId}::${current.roleId}`;
        if (visited.has(key)) return { status: 'unresolved', reason: 'cycle', path };
        visited.add(key);
        const bucket = resourceMap[current.executionId];
        if (!bucket) return { status: 'unresolved', reason: 'missing-entry', path };
        const entry = bucket[current.roleId] as (ResourceDataJson | ResourcePotentialInputJson | ResourcePotentialOutputJson | undefined);
        if (!entry) return { status: 'unresolved', reason: 'missing-entry', path };
        if (entry.kind === 'realized') return { status: 'realized', entry: entry as ResourceDataJson, path };
        if (entry.kind === 'potential-output') return { status: 'blocked-output', entry: entry as ResourcePotentialOutputJson, path };
        if (entry.kind === 'potential-input') {
            const pointer = (entry as ResourcePotentialInputJson).pendingRef;
            if (!pointer) return { status: 'unresolved', reason: 'missing-entry', path };
            current = pointer as ResourceSocketJson;
            continue;
        }
        return { status: 'unresolved', reason: 'missing-entry', path };
    }
    return { status: 'unresolved', reason: 'depth-exceeded', path };
}
// Job output augmented after extractor run
type JobOutput = { path: string; timestamp: string, extractedData?: JsonValueJson };


const nodeName = CONSTANTS_LOCAL.NODE_Work_Step;


export class NodeWorkStep extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {
        try {
            const workStep = state.workflowSpec.workflow.steps[state.stepCounter] as WorkStepJson; // safe to assert as WorkStepJson because EdgeRouting ensures that only WorkSteps reach here
            const execution = workStep.execution;

            const inputBindingMap = execution.roleBindings.inputBindingMap;
            const outputBindingMap = execution.roleBindings.outputBindingMap;
            const resourceMap = state.workflowSpec.resourceMaps?.[0] ?? {} as ResourceMapJson;

            const job = state.dataLib.jobDataMap.get(execution.jobId);
            if (!job) {
                throw new Error(`Job with ID ${execution.jobId} not found in dataLib.jobs`);
            }

            // console.log('job:', JSON.stringify(job, null, 2));

            const payload: Record<string, ResourceDataJson | ResourcePotentialOutputJson> = {};

            // Resolve inputs
            for (const inputRoleId of Object.keys(inputBindingMap)) {
                const inputRoleName = job.roles.inputMap[inputRoleId as RoleIdJson].name;
                const entry = resourceMap[execution.id]?.[inputRoleId as RoleIdJson] as (ResourceDataJson | ResourcePotentialInputJson | ResourcePotentialOutputJson | undefined);
                if (!entry) throw new Error(`Missing resource map entry for input role '${inputRoleId}' in execution '${execution.id}'`);
                if (entry.kind === 'realized') {
                    payload[inputRoleName] = entry;
                    continue;
                }
                if (entry.kind === 'potential-input') {
                    const pointer = (entry as ResourcePotentialInputJson).pendingRef;
                    const result = resolveResourceChain(resourceMap, pointer as ResourceSocketJson);
                    if (result.status === 'realized') {
                        payload[inputRoleName] = result.entry;
                        continue;
                    }
                    // Blocked or unresolved: halt execution gracefully
                    throw new Error(`Cannot realize input role '${inputRoleId}': ${result.status === 'blocked-output' ? 'blocked by future output' : 'unresolved chain (' + result.reason + ')'} `);
                }
                if (entry.kind === 'potential-output') {
                    throw new Error(`Input role '${inputRoleId}' unexpectedly bound to potential-output in same execution.`);
                }
                throw new Error(`Unsupported resource kind for input role '${inputRoleId}'`);
            }

            // Pass output potentials unchanged (will be materialized post job execution)
            for (const outputRoleId of Object.keys(outputBindingMap)) {
                const outputRoleName = job.roles.outputMap[outputRoleId as RoleIdJson].name;
                const pot = resourceMap[execution.id]?.[outputRoleId as RoleIdJson] as (ResourcePotentialOutputJson | undefined);
                if (!pot || pot.kind !== 'potential-output') {
                    throw new Error(`Expected potential-output entry for output role '${outputRoleId}'`);
                }
                payload[outputRoleName] = pot;
            }

            // console.log('payload:', JSON.stringify(payload, null, 2));

            const asyncWrapper = async (url: string): Promise<Record<RoleIdJson, JobOutput>> => {

                const response = await axios.post(
                    url,
                    payload,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        timeout: 30 * 60 * 1000, // 30 minutes in milliseconds
                    }
                );

                const result = response.data;

                // console.log('result:', JSON.stringify(result, null, 2));

                return result.outputs;
            }

            const roleNameOutputMap = await asyncWrapper(job.uri);

            // Build reverse lookup map from role name to role ID
            const roleNameToIdMap = new Map(
                Object.entries(job.roles.outputMap).map(([id, role]) => [role.name, id])
            );

            const roleIdOutputMap: Record<RoleIdJson, JobOutput> = {};

            // Map back from role names to role IDs
            for (const [roleName, output] of Object.entries(roleNameOutputMap)) {
                const id = roleNameToIdMap.get(roleName);
                if (!id) {
                    throw new Error(`Output role name '${roleName}' not found in job roles`);
                }
                roleIdOutputMap[id as RoleIdJson] = output;
            }

            // Here, for each output we must invoke the respective ResourceType's extractor job
            await Promise.all(Object.entries(roleIdOutputMap).map(async ([outputRoleId, output]) => {

                const path = output.path;
                const extractedData = await fetchData(path);

                // console.log('outputRole:', JSON.stringify(outputRole, null, 2));

                // Merge the extracted data with the output
                roleIdOutputMap[outputRoleId as RoleIdJson] = {
                    ...(output as any),
                    extractedData: extractedData as JsonValueJson
                };

            }));

            // Now outputs have the extractedData property added

            // Create new entries for resourceMaps[0] based on outputBindings
            // Convert potential-output entries to realized
            const updatedResourceMap: ResourceMapJson = { ...resourceMap };
            const execBucket = { ...updatedResourceMap[execution.id] };
            for (const outputRoleId of Object.keys(outputBindingMap)) {
                const pot = resourceMap[execution.id]?.[outputRoleId as RoleIdJson] as (ResourcePotentialOutputJson | undefined);
                if (!pot || pot.kind !== 'potential-output') continue; // skip if unexpectedly missing
                const outputInfo = roleIdOutputMap[outputRoleId as RoleIdJson];
                if (!outputInfo) continue;
                const extracted = outputInfo.extractedData as any;
                let identityValue: any = (extracted && typeof extracted !== 'object') ? extracted : (extracted?.identity ?? 0);
                const realized: ResourceDataJson = {
                    id: pot.id as ResourceIdJson,
                    typeId: pot.typeId,
                    creationContext: pot.creationContext,
                    kind: 'realized',
                    path: outputInfo.path,
                    timestamp: outputInfo.timestamp,
                    extractedData: {
                        identity: identityValue,
                        ...((typeof extracted === 'object' && extracted !== null) ? extracted : {})
                    } as any
                };
                execBucket[outputRoleId as RoleIdJson] = realized;
            }
            updatedResourceMap[execution.id] = execBucket;

            return {
                messages: [new AIMessage(`${nodeName} completed`)],
                workflowSpec: {
                    ...state.workflowSpec,
                    resourceMaps: [
                        updatedResourceMap,
                        ...state.workflowSpec.resourceMaps.slice(1)
                    ]
                },
                stepCounter: state.stepCounter + 1
            };

        } catch (error: any) {
            // Structured logging to help diagnose HTTP / Axios errors (ERR_BAD_REQUEST, etc.)
            try {
                // Use Axios config data (if present) rather than referencing local variables that
                // may be out of scope when the catch runs.
                const payloadPreview = error?.config?.data ?? null;
                console.error(`Error in ${nodeName}:`, {
                    message: error?.message,
                    code: error?.code,
                    status: error?.response?.status,
                    responseData: error?.response?.data,
                    requestUrl: error?.config?.url,
                    payload: payloadPreview,
                });
            } catch (logErr) {
                // Fallback if structured logging throws
                console.error(`Error in ${nodeName} (logging failed):`, error);
            }

            return {
                messages: [new AIMessage(`${nodeName} failed`)],
                workflowSpec: {
                    ...state.workflowSpec,
                },
                stepCounter: state.stepCounter + 1
            };
        }
    }
}



