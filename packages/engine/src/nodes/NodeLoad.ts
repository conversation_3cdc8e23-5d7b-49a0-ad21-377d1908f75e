import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@toolproof-npm/schema';
import { CONSTANTS } from '@toolproof-npm/shared/constants';
import * as CONSTANTS_LOCAL from '../constants.js';
import { listArchetypesMeta, listResourcesData } from '@toolproof-npm/shared/server';
import { BaseNode, GraphState } from '../types.js';
import { RunnableConfig } from '@langchain/core/runnables';
import { AIMessage } from '@langchain/core/messages';


const nodeName = CONSTANTS_LOCAL.NODE_LOAD;


export class NodeLoad extends BaseNode {

    constructor() {
        super(nodeName);
    }

    protected async executeNode(state: GraphState, options?: Partial<RunnableConfig<Record<string, any>>>): Promise<Partial<GraphState>> {

        if (state.stepCounter === 0) {
            try {
                console.log("GOOGLE_APPLICATION_CREDENTIALS_JSON : ", process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);
                const asyncWrapper = async () => {

                    // Load types
                    const types = await listArchetypesMeta<TypeMetaJson>(
                        CONSTANTS.ARCHETYPES.types,
                        {
                            [CONSTANTS.STORAGE.FILTER.members]: true,
                            [CONSTANTS.STORAGE.FILTER.specials]: false,
                        }
                    );

                    // Load jobs 
                    const { [CONSTANTS.SPECIALS.TYPE_Job]: jobResources = [] } =
                        await listResourcesData([CONSTANTS.SPECIALS.TYPE_Job]);

                    // Helper to construct jobs map
                    const makeJobDataMap = () => {
                        return jobResources.reduce((map, res) => {
                            const job = res.extractedData as unknown as JobJson;
                            map.set(res.id, job);
                            return map;
                        }, new Map<ResourceIdJson, JobJson>());
                    }

                    // Helper to construct type meta map
                    const makeTypeMetaMap = () => {
                        const members = types[CONSTANTS.STORAGE.FILTER.members] ?? [];
                        return members.reduce((map: Map<TypeIdJson, TypeMetaJson>, meta: any) => {
                            if (meta && typeof meta.id === 'string' && meta.id.startsWith('TYPE-')) {
                                map.set(meta.id as TypeIdJson, meta as TypeMetaJson);
                            } else {
                                throw new Error(`Invalid type meta id: ${meta?.id}`);
                            }
                            return map;
                        }, new Map<TypeIdJson, TypeMetaJson>());
                    };

                    // Construct dataLib
                    const dataLib = {
                        typeMetaMap: makeTypeMetaMap(),
                        jobDataMap: makeJobDataMap(),
                    };

                    return dataLib;
                };

                const dataLib = await asyncWrapper();

                return {
                    messages: [new AIMessage(`${nodeName} completed successfully`)],
                    dataLib
                };
            } catch (error: any) {
                throw new Error(`Error in ${nodeName}: ${error.message}`);
                /* console.error(`Error in ${nodeName}:`, error);
                return {
                    messages: [new AIMessage(`${nodeName} failed`)],
                    dataLib: new Map()
                }; */
            }
        } else {
            throw new Error(`${nodeName} can only be run at the start of the workflow`);
        }
    }

}



