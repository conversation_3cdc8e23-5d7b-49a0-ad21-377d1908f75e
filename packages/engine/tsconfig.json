{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "baseUrl": ".", "paths": {"shared/*": ["../shared/src/*"]}, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "skipLibCheck": true, "isolatedModules": true, "outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "incremental": true, "composite": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}