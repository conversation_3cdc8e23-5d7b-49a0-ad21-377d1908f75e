# CAFS API Server

A TypeScript Express server providing REST APIs for Content Addressable File Storage (CAFS) with Google Cloud Storage backend.

## Features

- **Content Addressable File Storage (CAFS)** with SHA-256 based deduplication
- **REST API endpoints** for storing and retrieving content
- **Google Cloud Storage** backend with Firestore metadata
- **Docker support** for easy deployment
- **TypeScript** with full type definitions
- **Health checks** and monitoring endpoints

## Quick Start

### Using Docker (Recommended)

1. Clone the repository
2. Copy environment variables:
   ```bash
   cp .env.example .env
   ```
3. Edit `.env` with your configuration
4. Build and run with Docker:
   ```bash
   docker-compose up --build
   ```

### Local Development

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Build the project:
   ```bash
   pnpm run build
   ```

3. Start the server:
   ```bash
   pnpm start
   ```

## API Endpoints

### Health Check
```
GET /health
```
Returns server health status.

### Store Content
```
POST /api/cafs/store
```

**Request Body:**
```json
{
  "meta": {
    "id": "unique-resource-id",
    "typeId": "document",
    "roleId": "content",
    "executionId": "execution-123"
  },
  "content": "Your content here as a string"
}
```

**Response:**
```json
{
  "success": true,
  "contentHash": "sha256-hash-of-content",
  "storagePath": "cafs/document/sha256-hash",
  "deduplicated": false,
  "message": "Content stored successfully"
}
```

### Retrieve Content
```
GET /api/cafs/retrieve/:contentHash?folder=cafs&updateAccessTime=true
```

**Parameters:**
- `contentHash`: SHA-256 hash of the content (64 hex characters)
- `folder`: Optional folder name (default: "cafs")
- `updateAccessTime`: Whether to update last access time (default: true)

**Response:**
```json
{
  "success": true,
  "contentHash": "sha256-hash-of-content",
  "content": "Retrieved content string",
  "retrievedAt": "2024-01-01T12:00:00.000Z"
}
```

### Check Content Existence
```
GET /api/cafs/exists/:contentHash?folder=cafs
```

**Response:**
```json
{
  "success": true,
  "contentHash": "sha256-hash-of-content",
  "exists": true,
  "checkedAt": "2024-01-01T12:00:00.000Z"
}
```

## Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Server Configuration
PORT=3000

# Google Cloud Storage Configuration
BUCKET_NAME=tp-resources

# Firestore Configuration
METADATA_COLLECTION=cafs_metadata

# Google Cloud Service Account (if not using default credentials)
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
```

## Docker Deployment

### Build Image
```bash
docker build -t cafs-api .
```

### Run Container
```bash
docker run -p 3000:3000 --env-file .env cafs-api
```

### Using Docker Compose
```bash
docker-compose up -d
```

### Push to Registry
```bash
# Tag for your registry
docker tag cafs-api your-registry.com/cafs-api:latest

# Push to registry
docker push your-registry.com/cafs-api:latest
```

## Development Scripts

```bash
# Install dependencies
pnpm install

# Build TypeScript
pnpm run build

# Start production server
pnpm start

# Start development server (build + run)
pnpm run start:dev

# Watch mode for development
pnpm run start:watch

# Lint code
pnpm run lint

# Fix linting issues
pnpm run lint:fix

# Clean build directory
pnpm run clean

# Docker commands
pnpm run docker:build
pnpm run docker:run
```

## Architecture

The server uses:
- **Express.js** for the REST API
- **CAFS class** for content-addressable storage logic
- **GCSUtils class** for Google Cloud Storage operations
- **SHA-256 hashing** for content addressing and deduplication
- **Firestore** for metadata storage
- **TypeScript** for type safety

## Error Handling

The API returns appropriate HTTP status codes:
- `200` - Success
- `201` - Content created
- `400` - Bad request (invalid input)
- `404` - Content not found
- `500` - Internal server error

All error responses include a descriptive error message.

## Security

- **Helmet.js** for security headers
- **CORS** enabled for cross-origin requests
- **Request size limits** (10MB default)
- **Non-root user** in Docker container
- **Health checks** for monitoring

## License

MIT
