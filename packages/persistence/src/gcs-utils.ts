import { Storage, File, GetFilesOptions } from '@google-cloud/storage';
import { createHash } from 'crypto';
import { IntegerInstance, ReadOptions, WriteOptions } from './types/index.js';
import { dbAdmin } from './firebaseAdminInit.js';
import { ExternallyProvidedResourceMeta } from '@toolproof-npm/shared/types'

/**
 * Core GCS utilities for reading and writing files
 */
export class GCSUtils {
    private storage: Storage;
    private bucketName: string;

    constructor(bucketName: string = 'tp-resources') {
        this.storage = new Storage();
        this.bucketName = bucketName;
    }

    /**
     * Reads a number value from a Google Cloud Storage file
     * @param filePath The path to the file in the GCS bucket
     * @param options Optional read options
     * @returns The numeric value from the file
     */
    async readFromGCS(filePath: string, options: ReadOptions = {}): Promise<number> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(filePath);

            // Check if file exists
            const [exists] = await file.exists();
            if (!exists) {
                throw new Error(`File ${filePath} does not exist in bucket ${this.bucketName}`);
            }

            const [fileContents] = await file.download();
            const jsonData: IntegerInstance = JSON.parse(fileContents.toString());

            if (typeof jsonData.identity !== 'number') {
                throw new Error(`File ${filePath} does not contain a valid number value`);
            }

            // Validate content hash if requested
            if (options.validateHash) {
                const contentHash = this.generateContentHash(fileContents.toString());
                const [metadata] = await file.getMetadata();
                const storedHash = metadata.metadata?.contentHash;

                if (storedHash && storedHash !== contentHash) {
                    throw new Error(`Content hash mismatch for file ${filePath}`);
                }
            }

            return jsonData.identity;
        } catch (error) {
            throw new Error(`Failed to read file ${filePath}: ${error}`);
        }
    }

    /**
     * Writes a number value to a Google Cloud Storage file
     * @param filePath The path where to store the file in the GCS bucket
     * @param identity The numeric value to store
     * @param options Optional write options
     */
    async writeToGCS(
        filePath: string,
        identity: number,
        options: WriteOptions = {}
    ): Promise<void> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(filePath);

            const jsonData: IntegerInstance = { identity };
            const jsonString = JSON.stringify(jsonData, null, 2);
            const contentHash = this.generateContentHash(jsonString);

            // Check if file exists and overwrite is not allowed
            if (!options.overwrite) {
                const [exists] = await file.exists();
                if (exists) {
                    throw new Error(`File ${filePath} already exists and overwrite is not allowed`);
                }
            }

            const custom: Record<string, string> = {
                contentHash,
                createdAt: new Date().toISOString(),
                ...Object.entries(options.metadata || {}).reduce((acc, [k, v]) => {
                    acc[k] = typeof v === 'string' ? v : JSON.stringify(v);
                    return acc;
                }, {} as Record<string, string>)
            };
            if (options.tags && options.tags.length > 0) {
                custom.tags = options.tags.join(',');
            }

            await file.save(jsonString, {
                metadata: {
                    contentType: options.contentType || 'application/json',
                    metadata: custom
                }
            });
        } catch (error) {
            throw new Error(`Failed to write file ${filePath}: ${error}`);
        }
    }

    async writeToFirestore(
        meta: ExternallyProvidedResourceMeta & {
            kind: string;
            timestamp: string;
            canonicalRef: string;
        }
    ): Promise<void> {
        try {
            const col = dbAdmin.collection('resources').doc(meta.typeId).collection('members');
            const docRef = col.doc(meta.id);
            await docRef.set(meta);
        } catch (error) {
            throw new Error(`Failed to write to Firestore: ${error}`);
        }
    }

    /**
     * Reads raw content from GCS
     * @param filePath The path to the file in the GCS bucket
     * @returns The raw file content as string
     */
    async readRawContent(filePath: string): Promise<string> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(filePath);

            const [exists] = await file.exists();
            if (!exists) {
                throw new Error(`File ${filePath} does not exist in bucket ${this.bucketName}`);
            }

            const [fileContents] = await file.download();
            return fileContents.toString();
        } catch (error) {
            throw new Error(`Failed to read raw content from ${filePath}: ${error}`);
        }
    }

    /**
     * Writes raw content to GCS
     * @param filePath The path where to store the file
     * @param content The content to store
     * @param contentType The MIME type of the content
     */
    async writeRawContent(
        content: string,
        meta: ExternallyProvidedResourceMeta & {
            kind: string;
            path: string;
            timestamp: string;
        }
    ): Promise<void> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(meta.path);
            // Flatten meta object into key/value pairs without assumptions.
            const flat: Record<string, string> = {};
            const flatten = (value: any, prefix: string) => {
                if (value === null || value === undefined) return;
                if (typeof value === 'object' && !Array.isArray(value)) {
                    for (const [k, v] of Object.entries(value)) {
                        const next = prefix ? `${prefix}.${k}` : k;
                        flatten(v, next);
                    }
                } else if (Array.isArray(value)) {
                    value.forEach((v, idx) => {
                        const next = `${prefix}[${idx}]`;
                        flatten(v, next);
                    });
                } else {
                    // Primitive: stringify safely
                    flat[prefix] = typeof value === 'string' ? value : JSON.stringify(value);
                }
            };
            flatten(meta, '');
            // Remove possible leading empty key produced if meta is primitive (unlikely)
            if (flat['']) delete flat[''];

            await file.save(content, {
                metadata: {
                    contentType: 'text/plain',
                    metadata: flat
                }
            });
        } catch (error) {
            throw new Error(`Failed to write raw content to ${meta.path}: ${error}`);
        }
    }

    /**
   * Checks if a file exists in GCS and returns its metadata id if available
   * @param filePath The path to check
   * @returns Object with existence flag and id (empty string if not found)
   */
    async fileExists(filePath: string): Promise<{ fileExists: boolean; id: string }> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(filePath);
            const [exists] = await file.exists();

            if (!exists) {
                return { fileExists: false, id: '' };
            }

            // Try to fetch metadata to extract custom metadata.id; return empty id on failure
            try {
                const [metadata] = await file.getMetadata();
                const metaMap: Record<string, string> = (metadata as any)?.metadata || {};
                // Attempt to locate a flattened key ending with '.id' or 'id'
                let id = metaMap.id || '';
                if (!id) {
                    const idKey = Object.keys(metaMap).find(k => k.endsWith('.id'));
                    if (idKey) id = metaMap[idKey] || '';
                }
                return { fileExists: true, id: typeof id === 'string' ? id : '' };
            } catch {
                return { fileExists: true, id: '' };
            }
        } catch (error) {
            return { fileExists: false, id: '' };
        }
    }

    /**
     * Generates SHA-256 hash of content
     * @param content The content to hash
     * @returns The SHA-256 hash as hex string
     */
    generateContentHash(content: string): string {
        return createHash('sha256').update(content).digest('hex');
    }


    /**
     * Deletes a file from GCS
     * @param filePath The path to the file to delete
     */
    async deleteFile(filePath: string): Promise<void> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(filePath);
            await file.delete();
        } catch (error) {
            throw new Error(`Failed to delete file ${filePath}: ${error}`);
        }
    }

    /**
     * Gets file metadata from GCS
     * @param filePath The path to the file
     * @returns File metadata
     */
    async getFileMetadata(filePath: string): Promise<any> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const file = bucket.file(filePath);
            const [metadata] = await file.getMetadata();
            return metadata;
        } catch (error) {
            throw new Error(`Failed to get metadata for file ${filePath}: ${error}`);
        }
    }

    /**
     * Lists files in the bucket with optional prefix
     * @param prefix Optional prefix to filter files
     * @returns Array of file names
     */
    async listFiles(prefix?: string): Promise<string[]> {
        try {
            const bucket = this.storage.bucket(this.bucketName);
            const options: GetFilesOptions = {};
            if (typeof prefix === 'string') {
                options.prefix = prefix; // only added when valid
            }

            const [files] = await bucket.getFiles(options);
            return files.map((file: File) => file.name);
        } catch (error) {
            throw new Error(`Failed to list files: ${error}`);
        }
    }
}
