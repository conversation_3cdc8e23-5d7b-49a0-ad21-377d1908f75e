/**
 * Express Server with CAFS APIs
 *
 * This server provides:
 * - REST API endpoints for Content Addressable File Storage (CAFS)
 * - Store content with deduplication
 * - Retrieve content by hash
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { CAFS } from './cafs.js';
import { GCSUtils } from './gcs-utils.js';
import type { CAFSOperationResult } from './types/index.js';
import type { ExternallyProvidedResourceMeta } from '@toolproof-npm/shared/types'


// Legacy exports for SDK usage
export * from './types/index.js';
export { CAFS, GCSUtils };

/**
 * Convenience function to create a GCSUtils instance
 * @param bucketName Optional bucket name
 * @returns GCSUtils instance
*/
export function createGCSUtils(bucketName?: string) {
    return new GCSUtils(bucketName);
}

/**
 * Convenience function to create a CAFS instance
 * @param config Optional configuration
 * @returns CAFS instance
*/
export function createCAFS(config?: any) {
    return new CAFS(config);
}

// Load environment variables
dotenv.config();

// Create Express app
const app: any = express();
const port = Number(process.env.PORT) || 3000;

// Initialize CAFS instance
const cafs = new CAFS({
    bucketName: process.env.BUCKET_NAME || 'tp-resources',
    metadataCollection: 'cafs_metadata',
    enableDeduplication: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    defaultContentType: 'application/json'
});

// Middleware
app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-device-agent']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (_req: express.Request, res: express.Response) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'CAFS API Server'
    });
});

// Store content endpoint
app.post('/api/cafs/store', async (req: express.Request, res: express.Response) => {
    try {
        const { meta, content } = req.body as { meta: ExternallyProvidedResourceMeta; content: string };

        // Validate required fields
        if (!meta || !content) {
            return res.status(400).json({
                error: 'Missing required fields: meta and content'
            });
        }

        if (!meta.id || !meta.typeId || !meta.creationContext.roleId || !meta.creationContext.executionId) {
            return res.status(400).json({
                error: 'Missing required meta fields: id, typeId, roleId, executionId'
            });
        }

        // Validate content is string
        if (typeof content !== 'string') {
            return res.status(400).json({
                error: 'Content must be a string'
            });
        }

        // Store content using CAFS
        const result: CAFSOperationResult = await cafs.storeContent(meta, content);

        if (result.success) {
            res.status(201).json({
                success: true,
                contentHash: result.contentHash,
                storagePath: result.storagePath,
                deduplicated: result.deduplicated,
                message: 'Content stored successfully'
            });
        } else {
            res.status(500).json({
                success: false,
                error: result.error || 'Failed to store content'
            });
        }

    } catch (error) {
        console.error('Error storing content:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error while storing content'
        });
    }
});

// Retrieve content endpoint
app.get('/api/cafs/retrieve/:contentHash', async (req: express.Request, res: express.Response) => {
    try {
        const { contentHash } = req.params;
        const { folder = '', updateAccessTime = 'true' } = req.query;

        // Validate content hash format (SHA-256 should be 64 hex characters)
        if (!/^[a-f0-9]{64}$/i.test(contentHash)) {
            return res.status(400).json({
                error: 'Invalid content hash format. Expected 64-character hex string.'
            });
        }

        const shouldUpdateAccessTime = updateAccessTime === 'true';

        const path = folder ? `${folder}/${contentHash}` : contentHash;

        // Retrieve content using CAFS
        const content = await cafs.retrieveContent(
            folder as string,
            path as string,
            shouldUpdateAccessTime
        );

        res.json({
            success: true,
            contentHash,
            content,
            retrievedAt: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error retrieving content:', error);

        if (error instanceof Error && error.message.includes('not found')) {
            res.status(404).json({
                success: false,
                error: `Content with hash ${req.params.contentHash} not found`
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Internal server error while retrieving content'
            });
        }
    }
});

// Check if content exists endpoint
app.get('/api/cafs/exists/:contentHash', async (req: express.Request, res: express.Response) => {
    try {
        const { contentHash } = req.params;
        const { folder = '' } = req.query;

        // Validate content hash format
        if (!/^[a-f0-9]{64}$/i.test(contentHash)) {
            return res.status(400).json({
                error: 'Invalid content hash format. Expected 64-character hex string.'
            });
        }

        const exists = await cafs.contentExists(folder as string, contentHash);

        res.json({
            success: true,
            contentHash,
            exists,
            checkedAt: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error checking content existence:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error while checking content existence'
        });
    }
});

// Global error handling
process.on('unhandledRejection', (reason: any) => {
    process.stderr.write('❌ Unhandled Rejection:', reason);
    process.exit(1);
});

process.on('uncaughtException', (err: any) => {
    process.stderr.write('❌ Uncaught Exception:', err);
    process.exit(1);
});

process.on('SIGINT', () => {
    console.log('Shutting down server gracefully...');
    process.exit(0);
});


// Start server (with HMR guard to prevent port collisions)
if (!(globalThis as any).__cafsServerStarted) {
    (globalThis as any).__cafsServerStarted = true;
    app.listen(port, '0.0.0.0', () => {
        console.log(`🚀 CAFS API Server running on port ${port}`);
        console.log(`📊 Health check: http://localhost:${port}/health`);
        console.log(`📝 Store content: POST http://localhost:${port}/api/cafs/store`);
        console.log(`📖 Retrieve content: GET http://localhost:${port}/api/cafs/retrieve/:contentHash`);
        console.log(`🔍 Check existence: GET http://localhost:${port}/api/cafs/exists/:contentHash`);
    }).on('error', (err: any) => {
        console.error('❌ Error starting server:', err);
        process.exit(1);
    });
}

// Export for testing
export default app;